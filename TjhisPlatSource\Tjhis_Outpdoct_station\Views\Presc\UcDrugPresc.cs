﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Outpdoct.Station.Views.CustomControls;
using Tjhis.Outpdoct.Station.Model;
using Tjhis.Outpdoct.Station.Diagnosis.Views;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Diagnosis.CommonSrv;
using Tjhis.Outpdoct.Station.Common;

using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using PlatCommon.Common;
using System.Collections;
using Tjhis.Outpdoct.Station.Business;
using PlatCommon.SysBase;
using Tjhis.Outpdoct.Station.Views.Print;
using Tjhis.Interface.Station.RA.YiTu;
using NM_Service.NMService;
using TjhisInterfaceInsurance;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace Tjhis.Outpdoct.Station.Views.Presc
{
    public partial class UcDrugPresc : UserControlTabBase
    {
        public override event EventHandler SaveSuccess;
        public event EventHandler barSave;
        public override string DefaultOrderClass => OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE;
        private IOrderPrint OrderPrint;
        public UcDrugPresc()
        {
            InitializeComponent();
            BsOrder = new BindingSource();
            BsOrder.DataSource = typeof(OUTP_ORDERS_STANDARD);
            this.GcOrders.DataSource = BsOrder;
            BsPrescList = new BindingSource();
            BsPrescList.DataSource = typeof(OUTP_ORDERS_STANDARD);
            this.GcPrescList.DataSource = BsPrescList;
            BsOrderCosts = new BindingSource();
            BsOrderCosts.DataSource = typeof(OUTP_ORDERS_COSTS_STANDARD);
            this.GcOrderCosts.DataSource = BsOrderCosts;
        }


        public void BindOpspDiseData(string patientId)
        {
            DataTable dtOpspDise = CommonDict.GetOpspDise(patientId);
            luePespDise.Properties.DataSource = dtOpspDise;
            luePespDise.Properties.DisplayMember = "OPSP_DISE_NAME";
            luePespDise.Properties.ValueMember = "OPSP_DISE_CODE";
        }

        private static void WriteDebugLog(string message)
        {
            try
            {
                string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LOG", "exLOG");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
                string logFile = Path.Combine(logDirectory, $"OrderDebug_{DateTime.Now:yyyyMMdd}.log");
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [CHECKPOINT] {message}";
                File.AppendAllText(logFile, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志写入失败: {ex.Message}");
            }
        }

        private void InitPrescControlData()
        {

            TxtPrescAttr.Properties.DataSource = CommonDict.DtPrescAttr;
            TxtPrescAttr.Properties.DisplayMember = "PRESC_ATTR_NAME";
            TxtPrescAttr.Properties.ValueMember = "PRESC_ATTR_NAME";
            SearchLookUpEditDept.DataSource = CommonDict.DtDeptDict;
            SearchLookUpEditDept.DisplayMember = "DEPT_NAME";
            SearchLookUpEditDept.ValueMember = "DEPT_CODE";
            LookUpEditItemClass.DataSource = CommonDict.DtOrderClassDict;
            LookUpEditItemClass.DisplayMember = "CLASS_NAME";
            LookUpEditItemClass.ValueMember = "CLASS_CODE";
            repositoryItemLookUpEdit2.DataSource = CommonDict.DtBasic;
            repositoryItemLookUpEdit3.DataSource = CommonDict.DtExpensive;
            repositoryItemLookUpEdit4.DataSource = CommonDict.DtChargeIndicator; 
        }
        /// <summary>
        /// 切换患者时触发
        /// </summary>
        /// <param name="outpPatientInfo"></param>
        /// <param name="orders"></param>
        public void ChangePatient(OutpPatientInfo outpPatientInfo,List<OUTP_ORDERS_STANDARD> orders)
        {
            this.PatientInfo = outpPatientInfo;
            this.PrescBusiness.SetPatientInfo(this.PatientInfo);
        }
        /// <summary>
        /// 病历修改时，触发
        /// </summary>
        public void ChangeMrAndDiag()
        {
            this.OutpMr = OrderBusiness.GetMrInfo();
            this.LuDiagDesc.Properties.DataSource = OrderBusiness.GetDtDiagnosis();//this.diagnosisInputSrv.GetOutpDiagnosis(this.PatientInfo.VISIT_DATE, this.PatientInfo.VISIT_NO);
            SetMrInfo();
        }
        public override void InitData(List<OUTP_ORDERS_STANDARD> orders,bool firstInit = true)
        {
            this.Orders = orders;
            this.PatientInfo = this.OrderBusiness.GetCurrentPatient();
            this.SetEditorReadonly(false);
            this.SetDiagnosis();
            try
            {
                this.OutpMr = OrderBusiness.GetMrInfo();
            }
            catch
            {
                this.OutpMr = null;
            }

            if (this.OutpMr != null && firstInit==true)
            {
                this.SetMrInfo();
            }
            
            this.InitPrescList();
        }


        public void SetBusiness(IOutpMr OutpMrBusiness,IOrders OrderBusiness,IPresc PrescBusiness)
        {
            this.OutpMrBusiness = OutpMrBusiness;
            this.OrderBusiness = OrderBusiness;
            this.PrescBusiness = PrescBusiness;
            this.PatientInfo = OrderBusiness.GetCurrentPatient();
            this.diagnosisInputSrv = new DiagnosisInputSrv();
        }
        public void InitInputControl()
        {
            ClinicInput = new OutpInputControl();
            ClinicInput.DictType = "门诊药品字典";
            ClinicInput.InitGcInputColumnAndData();
            ClinicInput.BindGridColumnAndEvent(this.GvOrders, "ORDER_TEXT");
            ClinicInput.SelectedItem += ClinicInput_SelectedItem;
            this.ClinicInput.SetInputCondition(" STORAGE = '" + this.lueDispensary.EditValue + "'");
            this.Controls.Add(ClinicInput);
        }


        public void InitControlData()
        {
            BsOrder.CurrentItemChanged += BsOrder_CurrentItemChanged;
            this.repSearchLookUpFrequency.DataSource = CommonDict.DtPerformFreqDict.Copy();
            this.repSearchLookUpFrequency.DisplayMember = "FREQ_DESC";
            this.repSearchLookUpFrequency.ValueMember = "FREQ_DESC";

            this.repSearchLookUpAdmin.DataSource = CommonDict.DtAdministrationDict.Copy();
            this.repSearchLookUpAdmin.DisplayMember = "ADMINISTRATION_NAME";
            this.repSearchLookUpAdmin.ValueMember = "ADMINISTRATION_NAME";
            Initstroages();
            
            #region 皮试内容
            // 是否皮试
            DataTable dtSkinFlag = new DataTable();
            dtSkinFlag.Columns.Add("SKINNAME", typeof(string));
            dtSkinFlag.Columns.Add("SKINCODE", typeof(Int32));
            dtSkinFlag.Rows.Add(new object[] { "皮试", 1 });
            dtSkinFlag.Rows.Add(new object[] { "续注", 2 });
            dtSkinFlag.Rows.Add(new object[] { "", 0 });
            dtSkinFlag.Rows.Add(new object[] { "免皮试", 3 });
            SkinFlagLookUpEdit.DataSource = dtSkinFlag;
            SkinFlagLookUpEdit.DisplayMember = "SKINNAME";
            SkinFlagLookUpEdit.ValueMember = "SKINCODE";

            DataTable dtSkinResult = new DataTable();
            dtSkinResult.Columns.Add("SKINNAME", typeof(string));
            dtSkinResult.Columns.Add("SKINCODE", typeof(string));
            dtSkinResult.Rows.Add(new object[] { "无结果", "0" });
            dtSkinResult.Rows.Add(new object[] { "阴性-", "1" });
            dtSkinResult.Rows.Add(new object[] { "弱陽性+", "2" });
            dtSkinResult.Rows.Add(new object[] { "陽性++", "3" });
            dtSkinResult.Rows.Add(new object[] { "中陽性+++", "4" });
            dtSkinResult.Rows.Add(new object[] { "强陽性++++", "5" });
            dtSkinResult.Rows.Add(new object[] { "持续用药", "9" });
            SkinResultLookUpEdit.DataSource = dtSkinResult;
            SkinResultLookUpEdit.DisplayMember = "SKINNAME";
            SkinResultLookUpEdit.ValueMember = "SKINCODE";
            #endregion
            Initstroages();
            InitPrescControlData();
        }

        private void Initstroages()
        {
            List<DeptDict> drugStores = PrescBusiness.GetDrugStore();
            lueDispensary.Properties.DataSource = drugStores;
            if (drugStores != null && drugStores.Count > 1)
            {
                lueDispensary.EditValue = drugStores[0].DEPT_CODE;
            }
        }

        public override void NewPresc()
        {
            this.OrderBusiness.wPresc.ClearInputResult();
            TxtPrescAttr.ReadOnly = false;
            if (this.lueDispensary.EditValue == null)
            {
                throw new MessageException("请选择药房！");
            }
            OUTP_ORDERS_STANDARD prescOrder = this.OrderBusiness.New();
            prescOrder = this.PrescBusiness.NewPresc(prescOrder, this.lueDispensary.EditValue.ToString());
            prescOrder.DIAGNOSIS_DESC = this.OrderBusiness.GetMrInfo().DIAG_DESC;
            this.BsPrescList.Add(prescOrder);
            this.BsPrescList.ResetBindings(false);
            DevGridViewHelper.GridViewLocateLastRow(GvPrestList, GvPrestList.RowCount - 1);
            int PrevFocusedRowHandle = this.GvPrestList.FocusedRowHandle;
            PrescFoucsRowChanged(prescOrder, PrevFocusedRowHandle);
            SendKeys.Send("{F9}");
            this.gridColumn6.Caption = this.gridColumn6.Caption.Replace("反选", "全选");
        }

        public void OrderCopy(List<OUTP_ORDERS_STANDARD> selectOrders)
        {
            List<OUTP_ORDERS_STANDARD> orderPresc = selectOrders.Where(o => OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(o.ORDER_CLASS)).ToList();
            if(orderPresc.Count > 0)
            {
                OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsPrescList);
                // 修复：如果没有处方或处方状态不是新建状态，则创建新方
                if (presc == null || !Constants.NEW_ORDER_STATE_STR.Equals(presc.STATE))
                {
                    this.NewPresc();
                }
                OUTP_ORDERS_STANDARD currentOrder = this.GetEditModel<OUTP_ORDERS_STANDARD>(BsOrder);
                List<OUTP_ORDERS_STANDARD> addOrders = this.OrderBusiness.OrderCopy(orderPresc);
                if (currentOrder != null && Constants.NEW_ORDER_STATE_STR.Equals(currentOrder.STATE) && string.IsNullOrEmpty(currentOrder.ORDER_TEXT))
                {
                    this.OrderBusiness.Remove(currentOrder);
                    this.BsOrder.Remove(this.BsOrder.Current);
                }
                addOrders.ForEach(order =>
                {
                    this.BsOrder.Add(order);
                });
                this.BsOrder.ResetBindings(false);
            }
        }
        public override void AddItem()
        {
            OUTP_ORDERS_STANDARD order = OrderBusiness.Add();
            this.BsOrder.Add(order);
            DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.DataRowCount - 1, ORDER_TEXT);
            SendKeys.Send("{F9}");
        }
        public override void Save()
        {
            // 日志：记录保存开始
            WriteDebugLog("[UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击");

            this.GvOrders.CloseEditor();
            bool updateDataSourceState = this.GvOrders.UpdateCurrentRow();
            List<OUTP_ORDERS_STANDARD> emptyOrders = new List<OUTP_ORDERS_STANDARD>();
            foreach (OUTP_ORDERS_STANDARD item in this.BsOrder.List)
            {
                if (string.IsNullOrEmpty(item.ORDER_CODE))
                {
                    emptyOrders.Add(item);
                }
            }
            emptyOrders.ForEach(o =>
            {
                this.BsOrder.Remove(o);
            });

            this.BsOrder.ResetBindings(false);
            List<OUTP_ORDERS_STANDARD> ordersSave = GetAddOrders();
            List<OUTP_ORDERS_STANDARD> allOrders = this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList();

            // 日志：记录待保存的医嘱
            WriteDebugLog($"[UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: {ordersSave?.Count ?? 0}");
            if (ordersSave != null)
            {
                for (int i = 0; i < ordersSave.Count; i++)
                {
                    var order = ordersSave[i];
                    WriteDebugLog($"[UcDrugPresc.Save] 待保存医嘱[{i + 1}] - CLINIC_NO: {order.CLINIC_NO}, ORDER_NO: {order.ORDER_NO}, ORDER_SUB_NO: {order.ORDER_SUB_NO}, ITEM_NO: {order.ITEM_NO}, ORDER_TEXT: {order.ORDER_TEXT}, ORDER_CLASS: {order.ORDER_CLASS}, STATE: {order.STATE}");
                }
            }
            ordersSave.ForEach(order =>
            {
                order.DIAGNOSIS_DESC = this.LuDiagDesc.EditValue?.ToString();
            });
            if (ordersSave!=null && ordersSave.Count>0)
            {
                try
                {
                    if (this.OrderBusiness.Save(ordersSave, allOrders))
                    {
                        // 日志：记录保存成功
                        WriteDebugLog("[UcDrugPresc.Save] 检查点[保存成功] - 西药保存成功");

                        //打印
                        if (ClinicParameter.SaveAutoPrint == "1")
                        {
                            //PrintPrescriptionReport(ordersSave);
                            //PrintPrescriptionReport_Changzhi(ordersSave);
                        }

                        this.BsOrder.Clear();
                        TxtPrescAttr.ReadOnly = true;
                        this.SaveSuccess?.Invoke(this, null);
                        MessageHelper.ShowSuccess("西药保存成功");
                    //启用医保事前审核 毁方不要执行
                    if(PatientInfo.CHARGE_TYPE.Contains("医保"))
                    {
                        #region 事前事中
                        string error = "";
                        TjhisInterfaceInsurance.MedicalInsurOut3101_02_03.SendInpBusinessHandle(PatientInfo.PATIENT_ID, PatientInfo.VISIT_NO.ToString(), "3101", ref error);
                        TjhisInterfaceInsurance.MedicalInsurOut3101_02_03.SendInpBusinessHandle(PatientInfo.PATIENT_ID, PatientInfo.VISIT_NO.ToString(), "3102", ref error);
                        #endregion
                    }

                    }
                    else
                    {
                        // 日志：记录保存失败
                        WriteDebugLog("[UcDrugPresc.Save] 错误: 保存失败，OrderBusiness.Save返回false");
                        this.BsOrder.List.Clear();
                    }
                }
                catch (Exception ex)
                {
                    // 日志：记录保存异常
                    WriteDebugLog($"[UcDrugPresc.Save] 错误: 保存异常: {ex.Message}");
                    throw;
                }
            }
            else
            {
                // 日志：记录无医嘱保存
                WriteDebugLog("[UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0");
            }
        }

        private void PrintPrescriptionReport(List<OUTP_ORDERS_STANDARD> ordersSave)
        {
            Hashtable hasParam = new Hashtable();
            DataSet dsPrint;
            string AppCode = "OUTPDOCT";
            int prescFlag = 0;
            hasParam.Add("ORDER_CLASS", ordersSave[0].ORDER_CLASS);
            hasParam.Add("CLINIC_NO", ordersSave[0].CLINIC_NO);
            hasParam.Add("APPOINT_NO", ordersSave[0].APPOINT_NO);
            hasParam.Add("OUTP_SERIAL_NO", ordersSave[0].SERIAL_NO);
            hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
            prescFlag = OrderPrint.GetPrescFlag(ordersSave[0].PRESC_ATTR, PrescParameter.PrescToxiProtitiesJingshen, PrescParameter.PrescToxiProtitiesDuma, CommonDict.DtToxity);
            if (!string.IsNullOrEmpty(ordersSave[0].SERIAL_NO))
            {
                switch (prescFlag)
                {
                    case 0:
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通西药", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_普通西药", dsPrint, false, AppCode);
                        break;
                    case 1:
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_精一精二", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_精一精二", dsPrint, false, AppCode);
                        break;
                    case 2:
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_毒麻", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_毒麻", dsPrint, false, AppCode);
                        break;
                }
            }
        }

        // 长治打印门诊医生处方单
        private void PrintPrescriptionReport_Changzhi(List<OUTP_ORDERS_STANDARD> ordersSave)
        {
            Hashtable hasParam = new Hashtable();
            DataSet dsPrint;
            string AppCode = "OUTPDOCT";
            int prescFlag = 0;
            hasParam.Add("ORDER_CLASS", ordersSave[0].ORDER_CLASS);
            hasParam.Add("CLINIC_NO", ordersSave[0].CLINIC_NO);
            hasParam.Add("APPOINT_NO", ordersSave[0].APPOINT_NO);
            hasParam.Add("OUTP_SERIAL_NO", ordersSave[0].SERIAL_NO);
            hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
            if (!string.IsNullOrEmpty(ordersSave[0].SERIAL_NO))
            {
                switch (ordersSave[0].PRESC_ATTR)
                {
                    // 普通西药
                    case "普通处方":
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通西药（长治）", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_普通西药（长治）", dsPrint, false, AppCode);
                        break;
                    // 精二
                    case "精神二类处方":
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_精二（长治）", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_精二（长治）", dsPrint, false, AppCode);
                        break;
                    // 精一、麻醉
                    case "精神一类处方":
                    case "毒麻处方":
                        dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_麻醉、精一（长治）", hasParam, AppCode);
                        XtraReportHelper.Print("门诊医生处方单_麻醉、精一（长治）", dsPrint, false, AppCode);
                        break;
                }
            }
        }
        public override void DelItem()
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);
            if (order == null)
            {
                return;
            }
            if (!Constants.NEW_ORDER_STATE_STR.Equals(order.STATE))
            {
                throw new MessageException("已保存的处方不允许删除，请使用毁方功能");
            }
            List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders(order);
            if(order.ORDER_SUB_NO == 1 && addOrders.Count(o=>o.ORDER_NO == order.ORDER_NO) > 0)
            {
                throw new MessageException("该药品为组合医嘱的第一条，不允许删除");
            }
            this.PrescBusiness.SetSubPrescFlag(addOrders);
            this.OrderBusiness.Remove(order);
            this.BsOrder.Remove(order);
        }
        public override void Del()
        {
            List<OUTP_ORDERS_STANDARD> delOrders = new List<OUTP_ORDERS_STANDARD>();
            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(BsPrescList);
            if (presc != null && Constants.NEW_ORDER_STATE_STR.Equals(presc.STATE))
            {
                this.BsPrescList.Remove(presc);
                this.BsPrescList.ResetBindings(false);
                this.BsOrder.Clear();
                return;
            }
            foreach (OUTP_ORDERS_STANDARD item in this.BsOrder.List)
            {
                delOrders.Add(item);
            }
            delOrders.ForEach(o =>
            {
                if (o.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
                {
                    this.OrderBusiness.Remove(o);
                    this.BsOrder.Remove(o);
                }
            });
            if (delOrders.Count(del => !del.STATE.Equals(Constants.NEW_ORDER_STATE_STR)) > 0)
            {
                if (this.OrderBusiness.Delete(delOrders))
                {
                    this.SaveSuccess?.Invoke(this, null);
                }
            }
        }

        public override void SubPresc()
        {
            OUTP_ORDERS_STANDARD currentOrder = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);
            List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders();
            if (addOrders.Count == 0)
            {
                throw new Exception("不能在已经保存过的处方上设置子处方！");
            }
            this.PrescBusiness.SetSubPresc(addOrders, currentOrder);
            //this.PrescBusiness.SetSubPrescFlag(addOrders);
            BsOrder.ResetBindings(false);
            DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, gridColumn19);
        }
        public override void AddItems(List<InputResult> inputResults)
        {
            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsPrescList);
            if (!Constants.NEW_ORDER_STATE_STR.Equals(presc.STATE))
            {
                this.NewPresc();
            }
            List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders();
            List<InputResult> addResult = inputResults.Where(result => OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE.Equals(result.ItemClass)).ToList();
            if (addResult.Count > 0)
            {
                OUTP_ORDERS_STANDARD currentOrder = this.GetEditModel<OUTP_ORDERS_STANDARD>(BsOrder);
                if(currentOrder != null && Constants.NEW_ORDER_STATE_STR.Equals(currentOrder.STATE) && string.IsNullOrEmpty(currentOrder.ORDER_TEXT))
                {
                    this.OrderBusiness.Remove(currentOrder);
                    this.BsOrder.Remove(this.BsOrder.Current);
                }
                addOrders = this.OrderBusiness.Add(addResult, addOrders);
                addOrders.ForEach(order =>
                {
                    this.BsOrder.Add(order);
                });
                this.BsOrder.ResetBindings(false);
            }
            
        }
        public override void ResetData()
        {
            base.ResetData();
            this.BsOrder.Clear();
            this.BsPrescList.Clear();
            this.BsOrder.ResetBindings(false);
            this.BsPrescList.ResetBindings(false);
            this.Initstroages();
            SetDiagnosis();
            this.OutpMr = null;
            this.TxtIllnessDesc.EditValue = "";
            this.TxtMedHistory.EditValue = "";
        }
        public override void SaveModel()
        {
            List<OUTP_ORDERS_STANDARD> orders = this.BsOrder.Cast<OUTP_ORDERS_STANDARD>().ToList();
            FrmSaveTemplate frm = new FrmSaveTemplate();
            frm.Orders = orders;
            frm.ShowDialog();
            frm.Dispose();
        }

        /// <summary>
        /// 刷新数据 - 重新从数据库加载处方数据
        /// </summary>
        public override void RefrushData()
        {
            WriteDebugLog("[UcDrugPresc.RefrushData] 开始刷新数据");

            // 重新加载医嘱数据
            this.Orders = this.OrderBusiness.GetOrdersList();
            List<OUTP_ORDERS_COSTS_STANDARD> costs = this.OrderBusiness.GetOrderCostsList();
            this.OrderBusiness.BingOrdersCosts(Orders, costs);
            this.OrderBusiness.SetMaxOrderNo(this.Orders);

            // 同步更新AddOrders集合，确保与数据库状态一致
            this.OrderBusiness.AddOrders.Clear();
            this.Orders.ForEach(order => {
                this.OrderBusiness.AddOrders.Add(order);
            });

            // 重新初始化处方列表
            this.InitPrescList();

            // 定位到最后一行
            if (GvPrestList.RowCount > 0)
            {
                DevGridViewHelper.GridViewLocateLastRow(GvPrestList, GvPrestList.RowCount - 1);
            }

            WriteDebugLog($"[UcDrugPresc.RefrushData] 刷新完成 - 处方数量: {GvPrestList.RowCount}");
        }
        protected override string FileName => "UcDrugPresc";
        protected OutpPatientInfo PatientInfo { get; set; }
        protected OutpMr OutpMr { get; set; }

        protected IOutpMr OutpMrBusiness { get; set; }
        private DiagnosisInputSrv diagnosisInputSrv { get; set; }
        private IPresc PrescBusiness { get; set; }
        private IOrders OrderBusiness { get; set; }
        private OutpInputControl ClinicInput { get; set; }
        private BindingSource BsOrder { get; set; }

        private List<OUTP_ORDERS_STANDARD> Orders { get; set; }
        private BindingSource BsPrescList { get; set; }
        private void ClinicInput_SelectedItem(InputResult result)
        {
            bool addState = false;
            OUTP_ORDERS_STANDARD order = this.GetEditOrder();
            if (null == order)
            {
                return;
            }
            int focusedRowHandle = GvOrders.FocusedRowHandle;
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                string strMsg = "";
                addState = OrderBusiness.SetClinciItem(order, GetAddOrders(order), result,ref strMsg);
                if (!addState&&!string.IsNullOrEmpty(strMsg))
                {
                    throw new MessageException(strMsg);
                }
                CalculateYJDX();
            });
            GvOrders.FocusedRowHandle = focusedRowHandle;
            if (addState)
            {
                if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))
                {
                    if (string.IsNullOrEmpty(order.ADMINISTRATION))
                    {
                        DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, ADMINISTRATION);
                        SendKeys.SendWait("{F4}");
                    }
                    else
                    {
                        DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, FREQUENCY);
                        SendKeys.SendWait("{F4}");
                    }
                }
                else
                {
                    DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, FREQ_DETAIL);
                }
            }
            else
            {
                OrderBusiness.Clear(order);
            }
            BsOrder.ResetCurrentItem();
        }

        private void SetMrInfo()
        {
            this.TxtIllnessDesc.EditValue = this.OutpMr.ILLNESS_DESC;
            this.TxtMedHistory.EditValue = this.OutpMr.MED_HISTORY;
        }

        private void SetEditorReadonly(bool isCanEditor)
        {
            this.LuDiagDesc.ReadOnly = isCanEditor;
            this.lueDispensary.ReadOnly = isCanEditor;
        }
        private List<OUTP_ORDERS_STANDARD> GetAddOrders(OUTP_ORDERS_STANDARD currentOrder = null)
        {
            List<OUTP_ORDERS_STANDARD> orders = new List<OUTP_ORDERS_STANDARD>();
            foreach (OUTP_ORDERS_STANDARD item in this.BsOrder.List)
            {
                if (item.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
                {
                    if(TxtPrescAttr.EditValue!=null&&!string.IsNullOrEmpty(TxtPrescAttr.EditValue.ToString()))
                    {
                        item.PRESC_ATTR = TxtPrescAttr.EditValue.ToString();
                    }
                    if (luePespDise.EditValue != null && !string.IsNullOrEmpty(luePespDise.EditValue.ToString()))
                    {
                        item.OPSP_DISE_CODE = luePespDise.EditValue.ToString();
                    }
                    if((item.PRESC_ATTR== "慢特处方")&&string.IsNullOrEmpty(item.OPSP_DISE_CODE))
                        throw new MessageException("请选择慢特病种！");
                    orders.Add(item);
                }
            }
            if (currentOrder != null)
            {
                orders?.Remove(currentOrder);
            }
            return orders;
        }
        private void GvOrders_ShowingEditor(object sender, CancelEventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                OUTP_ORDERS_STANDARD order = this.GetEditOrder();
                List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders(order);
                //addOrders.Remove(order);
                string filedName = this.GvOrders.FocusedColumn.FieldName;
                e.Cancel = !OrderBusiness.CanShowEditor(order, addOrders, filedName);
            });
        }


        private void BindPtescList(List<OUTP_ORDERS_STANDARD> prescList)
        {
            this.BsPrescList.Clear();
            prescList.ForEach(fpresc =>
            {
                this.BsPrescList.Add(fpresc);
            });
            this.BsPrescList.ResetBindings(false);

        }
        private void InitPrescList()
        {
            List<OUTP_ORDERS_STANDARD> prescList = PrescBusiness.GetPrescList(this.Orders);
            BindPtescList(prescList);
            BsPrescList.Position = GvPrestList.FocusedRowHandle;

            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsPrescList);
            if (null == presc)
            {
                List<DeptDict> drugStores = lueDispensary.Properties.DataSource as List<DeptDict>;
                if (string.IsNullOrWhiteSpace(this.lueDispensary.EditValue?.ToString()) && drugStores != null && drugStores.Count > 0)
                {
                    this.lueDispensary.EditValue = drugStores[0].DEPT_CODE;
                }
                //this.lueDispensary.EditValue = "";
                this.LuDiagDesc.SetEditValue("");

                if (string.IsNullOrWhiteSpace(this.TxtPrescAttr.EditValue?.ToString()))
                {
                    this.TxtPrescAttr.EditValue = PrescParameter.PrescAttr;
                }

                this.BsOrder.Clear();
                BsOrder.ResetBindings(false);
            }
        }

        private void LuDiagDesc_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (!"新增诊断".Equals(e.Button.Caption))
            {
                return;
            }
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                if (this.PatientInfo == null)
                {
                    throw new MessageException("请选择一个病人");
                }
                FrmDiagnosisInput frmDiags = new FrmDiagnosisInput();
                frmDiags.CurrentOutpPatientInfo = this.PatientInfo;
                frmDiags.OutpMr = this.OutpMr;
                frmDiags.StartPosition = FormStartPosition.CenterScreen;
                frmDiags.ShowDialog();
                if (frmDiags.DialogResult == DialogResult.OK)
                {
                    this.SetDiagnosis();
                    this.SaveOutMr();

                    ((FrmOutpPatientList)this.ParentForm).uom.OnDiagnosisChanged();
                }
            });
        }
        private void SaveOutMr()
        {
            this.OutpMrBusiness.SaveOrUpdate(OutpMr);
        }

        private void SetDiagnosis()
        {
            this.LuDiagDesc.Properties.DataSource = this.OrderBusiness.GetDtDiagnosis();
        }
        private OUTP_ORDERS_STANDARD GetEditOrder()
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);
            return order;
        }
        private void GvOrders_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                OUTP_ORDERS_STANDARD order = this.GetEditOrder();
                List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders();
                if (order == null)
                {
                    return;
                }
                //如果没有医嘱类别，则是代表改记录是新增的空记录
                if (string.IsNullOrEmpty(order.ORDER_CLASS))
                {
                    return;
                }
                string fieldName = e.Column.FieldName;

                // 添加调试日志
                WriteDebugLog($"[UcDrugPresc.GvOrders_CellValueChanged] 字段修改 - fieldName: {fieldName}, 药品名称: {order.ORDER_TEXT}, 药品代码: {order.ORDER_CODE}, 新值: {e.Value}");

                if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))
                {
                    int iFocusedRowHandle = GvOrders.FocusedRowHandle;
                    if (fieldName == "AMOUNT")
                    {
                        WriteDebugLog($"[UcDrugPresc.GvOrders_CellValueChanged] AMOUNT字段修改，调用CalculateYJDX - 药品名称: {order.ORDER_TEXT}, 新数量: {e.Value}");
                        CalculateYJDX();
                    }
                    else
                    {
                        WriteDebugLog($"[UcDrugPresc.GvOrders_CellValueChanged] 其他字段修改，调用PrescBusiness.CellValueChanged - fieldName: {fieldName}");
                        Decimal? originalAmount = order.AMOUNT;
                        PrescBusiness.CellValueChanged(fieldName, order, this.GetAddOrders());
                        if(originalAmount!=order.AMOUNT)
                            CalculateYJDX();
                    }
                    DevGridViewHelper.GridViewLocateEditColumns(GvOrders, iFocusedRowHandle, ABIDANCE);
                    SendKeys.SendWait("{F4}");
                }

            });
        }

        private void repSearchLookUpAdmin_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            if (e.CloseMode == PopupCloseMode.Normal)
            {
                OUTP_ORDERS_STANDARD order = this.GetEditOrder();
                if (order == null) return;
                List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders(order);
                SearchLookUpEdit slook = sender as SearchLookUpEdit;
                DataRow dr = slook.Properties.View.GetFocusedDataRow();
                if (dr == null) return;
                this.PrescBusiness.SetPatientInfo(PatientInfo);
                order.ADMINISTRATION = dr["ADMINISTRATION_NAME"].ToString();
                this.PrescBusiness.CellValueChanged("ADMINISTRATION", order, addOrders);
                this.BsOrder.ResetBindings(false);
                DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, FREQUENCY);
                SendKeys.SendWait("{F4}");

            }
        }

        private void repSearchLookUpFrequency_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            if (e.CloseMode == PopupCloseMode.Normal)
            {
                SearchLookUpEdit slook = sender as SearchLookUpEdit;
                DataRow dr = slook.Properties.View.GetFocusedDataRow();
                if (dr == null) return;
                OUTP_ORDERS_STANDARD order = this.GetEditOrder();
                if (order == null) return;
                //presc.SetFrequency(order, dr);
                //同组医嘱频次同步
                order.FREQUENCY = dr["FREQ_DESC"].ToString();
                PrescBusiness.CellValueChanged("FREQUENCY", order, GetAddOrders());
                DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.FocusedRowHandle, DOSAGE);
                this.BsOrder.ResetBindings(false);
            }
        }
        

        private void lueDispensary_EditValueChanged(object sender, EventArgs e)
        {
            if (this.ClinicInput != null)
            {
                this.ClinicInput.SetInputCondition(" STORAGE = '" + this.lueDispensary.EditValue + "'");
            }
            if (String.IsNullOrWhiteSpace(lueDispensary.EditValue?.ToString()))
            {
                List<DeptDict> drugStores = PrescBusiness.GetDrugStore();
                lueDispensary.Properties.DataSource = drugStores;
                if (drugStores != null && drugStores.Count >= 1)
                {
                    lueDispensary.EditValue = drugStores[0].DEPT_CODE;
                }
            }
        }

        private void GcOrders_EditorKeyDown(object sender, KeyEventArgs e)
        {
            GridColumn gridColumn = this.GvOrders.FocusedColumn;
            if (null == gridColumn)
            {
                return;
            }
            if (e.KeyCode == Keys.Enter && gridColumn.FieldName.Equals("FREQ_DETAIL"))
            {
                this.ExcuteExcetionMethodChangeCursor(() =>
                {

                    OUTP_ORDERS_STANDARD order = OrderBusiness.Add();
                    this.BsOrder.Add(order);

                    DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.DataRowCount - 1, gridColumn12);
                    SendKeys.Send("{F9}");
                });
            }
        }

        private void GvPrestList_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            TxtPrescAttr.ReadOnly = true;
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                PrescFoucsRowChanged(e.RowHandle);
            });
        }
        private void PrescFoucsRowChanged(OUTP_ORDERS_STANDARD prescOrder,int PrevFocusedRowHandle)
        {
            List<OUTP_ORDERS_STANDARD> editorList = this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (editorList.Count(c => Constants.NEW_ORDER_STATE_STR.Equals(c.STATE)) > 0)
            {
                this.GvPrestList.FocusedRowHandle = PrevFocusedRowHandle;
                throw new MessageException("请先保存当前处方");
            }
            this.lueDispensary.EditValue = "";
            this.lueDispensary.EditValue = prescOrder.PERFORMED_BY;
            bool isCanEditor = !prescOrder.STATE.Equals(Constants.NEW_ORDER_STATE_STR);
            if (isCanEditor)
            {
                DataTable dt_diag = new DataTable();
                DataColumn dc_diag = new DataColumn("DIAGNOSIS_DESC", typeof(string));
                dt_diag.Columns.Add(dc_diag);
                DataRow dr_diag = dt_diag.NewRow();
                dr_diag["DIAGNOSIS_DESC"] = prescOrder.DIAGNOSIS_DESC;
                dt_diag.Rows.Add(dr_diag);
                this.LuDiagDesc.Properties.DataSource = dt_diag;
            }
            else
            {
                this.LuDiagDesc.Properties.DataSource = this.OrderBusiness.GetDtDiagnosis();
            }
            this.LuDiagDesc.EditValue = prescOrder.DIAGNOSIS_DESC;
            this.TxtPrescAttr.EditValue = prescOrder.PRESC_ATTR;
            this.SetEditorReadonly(isCanEditor);

            this.BsOrder.Clear();
            int insurCount = 0;
            this.Orders.ForEach(order =>
            {
                if (prescOrder.APPOINT_NO.Equals(order.APPOINT_NO))
                {
                    if (order.INSUR_ADULT.Equals("0"))
                    {
                        insurCount++;
                    }
                    this.BsOrder.Add(order);
                }
            });
            if (!isCanEditor)
            {
                OUTP_ORDERS_STANDARD drug = this.OrderBusiness.Add();
                this.BsOrder.Add(drug);
                DevGridViewHelper.GridViewLocateEditColumns(GvOrders, GvOrders.DataRowCount - 1, ORDER_TEXT);
            }
            BsOrder.ResetBindings(false);
        }
        protected string CheckAllStatus { get; set; }
        private void GvOrders_CustomDrawColumnHeader(object sender, DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventArgs e)
        {
            if(e.Column != null && e.Column.FieldName.Equals("INSUR_ADULT"))
            {
            }
        }

        

        private void GvOrders_Click(object sender, EventArgs e)
        {
            OUTP_ORDERS_STANDARD prescList = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsPrescList);
            if (prescList == null)
            {
                return;
            }
            if (!Constants.NEW_ORDER_STATE_STR.Equals(prescList.STATE))
            {
                return;
            }
            DevGridViewHelper.ClickGridCheckBox(this.GvOrders, "INSUR_ADULT", CheckAllStatus, "STATE", Constants.NEW_ORDER_STATE_STR);
            this.BsOrder.ResetBindings(false);
        }

        private void RcINSURANCE_FALG_CheckStateChanged(object sender, EventArgs e)
        {
            OUTP_ORDERS_STANDARD drugItem = this.GetEditModel<OUTP_ORDERS_STANDARD>(BsOrder);
            if ((sender as CheckEdit).Checked)
            {
                drugItem.INSURANCE_FLAG = "1";
            }
            else
            {
                drugItem.INSURANCE_FLAG = "0";
            }
        }

        public override void Print()
        {
            FrmOutpPrint frm = new FrmOutpPrint();
            frm.StartPosition = FormStartPosition.CenterScreen;
            frm.CurrentPatientInfo = this.PatientInfo;
            frm.DeptCode = GlobalValue.DeptCode;
            frm.AppCode = GlobalValue.AppCode;
            frm.ShowDialog();
            //if (OrderPrint == null)
            //{
            //    OrderPrint = new OrderPrintBusiness();
            //}
            //OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);
            //if (order == null)
            //    return;
            ////打印
            //Hashtable hasParam = new Hashtable();
            //DataSet dsPrint;
            //string AppCode = "OUTPDOCT";
            //int prescFlag = 0;
            //hasParam.Add("ORDER_CLASS", order.ORDER_CLASS);
            //hasParam.Add("CLINIC_NO", order.CLINIC_NO);
            //hasParam.Add("APPOINT_NO", order.APPOINT_NO);
            //hasParam.Add("OUTP_SERIAL_NO", order.SERIAL_NO);
            //prescFlag = OrderPrint.GetPrescFlag(order.PRESC_ATTR, PrescParameter.PrescToxiProtitiesJingshen, PrescParameter.PrescToxiProtitiesDuma, CommonDict.DtToxity);
            //if (!string.IsNullOrEmpty(order.SERIAL_NO))
            //{
            //    switch (prescFlag)
            //    {
            //        case 0:
            //            dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_普通西药", hasParam, AppCode);
            //            XtraReportHelper.Print("门诊医生处方单_普通西药", dsPrint, false, AppCode);
            //            break;
            //        case 1:
            //            dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_精一精二", hasParam, AppCode);
            //            XtraReportHelper.Print("门诊医生处方单_精一精二", dsPrint, false, AppCode);
            //            break;
            //        case 2:
            //            dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊医生处方单_毒麻", hasParam, AppCode);
            //            XtraReportHelper.Print("门诊医生处方单_毒麻", dsPrint, false, AppCode);
            //            break;
            //    }
            //}
        }

        // 打印注射单
        public void TransfusionClincked(object sender, MouseEventArgs e)
        {
            OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);
            if (order == null)
            {
                XtraMessageBox.Show("请选择要打印的处方", "提示");
                return;
            }
            else
            {
                if (order.APPOINT_NO == null || order.APPOINT_NO == string.Empty || order.APPOINT_NO.ToString() == "新开")
                {
                    XtraMessageBox.Show("请选择要打印的处方!", "提示");
                    return;
                }

                string outpserialno = order.OUTP_SERIAL_NO;
                string outpapp_no = order.APPOINT_NO;
                string order_dept = order.ORDERED_BY;
                if (!order_dept.Equals(GlobalValue.DeptCode))
                {
                    XtraMessageBox.Show("不是本科室开的处方不能打印", "提示");
                    return;
                }

                FrmPrintOutp fp = new FrmPrintOutp();
                fp.clinicNo = order.CLINIC_NO;
                fp.serialNo = outpserialno;
                fp.appointNo = outpapp_no;
                fp.AppCode = GlobalValue.AppCode;
                fp.prnType = "注射通知单";
                fp.ShowDialog();
            }
        }

        private void UcDrugPresc_Load(object sender, EventArgs e)
        {
            OrderPrint = new OrderPrintBusiness();
        }

        private void SkinResult_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Frmskintest frm = new Frmskintest();
            frm.ClinicNo = PatientInfo.CLINIC_NO;
            if (frm.ShowDialog() == DialogResult.OK)
            {
                List<OUTP_ORDERS_STANDARD> Orders = OrderBusiness.GetOrdersList();
                List<OUTP_ORDERS_COSTS_STANDARD> OrderCosts = OrderBusiness.GetOrderCostsList();
                OrderBusiness.BingOrdersCosts(Orders, OrderCosts);
                OrderBusiness.SetMaxOrderNo(Orders);
                PrescBusiness.SetSubPrescFlag(Orders);
                InitData(Orders,false);
            }
        }
        private void GvPrestList_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            base.GvChargeIndicatorRowCellStyle(sender, e);
        }

        private void GvPrestList_BeforeLeaveRow(object sender, DevExpress.XtraGrid.Views.Base.RowAllowEventArgs e)
        {
            if (e.RowHandle < 0) return;
            if (needSave())
            {
                XtraMessageBox.Show("请保存当前西药处方！", "提示");
                e.Allow = false;
            }
        }

        /// <summary>
        /// 删除单个项目
        /// </summary>
        /// <param name="delOrder"></param>
        private void DelItem(OUTP_ORDERS_STANDARD delOrder)
        {
            this.OrderBusiness.Remove(delOrder);
            this.BsOrder.Remove(delOrder);
        }
        /// <summary>
        /// 删除空白项目行
        /// </summary>
        /// <param name="delOrder"></param>
        private void DelEmptyItem(OUTP_ORDERS_STANDARD delOrder)
        {
            if (delOrder == null) return;
            if (Constants.NEW_ORDER_STATE_STR.Equals(delOrder.STATE)
                && (string.IsNullOrEmpty(delOrder.ORDER_TEXT) || string.IsNullOrEmpty(delOrder.ORDER_CODE)))
            {
                DelItem(delOrder);
            }
        }
        /// <summary>
        /// 删除多列药品空行
        /// </summary>
        /// <returns></returns>
        private void DelEmptyRows(List<OUTP_ORDERS_STANDARD> delOrders)
        {
            if (delOrders == null) return;
            delOrders.ForEach(item =>
            {
                DelEmptyItem(item);
            });
        }
        /// <summary>
        /// 删除空白药品行
        /// </summary>
        /// <returns></returns>
        private List<OUTP_ORDERS_STANDARD> GetAddOrdersAndDleteEmpty()
        {
            List<OUTP_ORDERS_STANDARD> orders = new List<OUTP_ORDERS_STANDARD>();
            List<OUTP_ORDERS_STANDARD> delOrders = new List<OUTP_ORDERS_STANDARD>();
            foreach (OUTP_ORDERS_STANDARD item in this.BsOrder.List)
            {
                if (item.STATE.Equals(Constants.NEW_ORDER_STATE_STR)
                    && !string.IsNullOrEmpty(item.ORDER_CODE) && !string.IsNullOrEmpty(item.ORDER_TEXT))
                {
                    orders.Add(item);
                }
                else if (item.STATE.Equals(Constants.NEW_ORDER_STATE_STR))
                {
                    delOrders.Add(item);
                }
            }
            DelEmptyRows(delOrders);
            return orders;
        }
        /// <summary>
        /// 检测是否有未保存的数据
        /// </summary>
        /// <returns></returns>
        public bool IsHaveNoSavePrescData()
        {
            bool bResult = false;
            List<OUTP_ORDERS_STANDARD> editorList = this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            List<OUTP_ORDERS_STANDARD> PrecEditorList = this.BsPrescList.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            if (editorList.Count(c => Constants.NEW_ORDER_STATE_STR.Equals(c.STATE)) > 0
                            || PrecEditorList.Count(c => Constants.NEW_ORDER_STATE_STR.Equals(c.STATE)) > 0)
            {
                editorList = GetAddOrdersAndDleteEmpty();
                if (editorList.Count > 0)
                {
                    bResult = true;
                }
            }
            return bResult;
        }
        /// <summary>
        /// 验证是否保存
        /// </summary>
        /// <returns></returns>
        public bool needSave()
        {
            if (IsHaveNoSavePrescData())
            {
                if (XtraMessageBox.Show("是否保存当前处方?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
                {
                    WriteDebugLog("[needSave] 用户选择保存处方，直接调用保存逻辑");

                    // 用户选择保存，直接调用保存方法
                    try
                    {
                        this.Save();
                        WriteDebugLog("[needSave] 保存完成");
                    }
                    catch (Exception ex)
                    {
                        WriteDebugLog($"[needSave] 保存异常: {ex.Message}");
                        throw; // 重新抛出异常，让上层处理
                    }
                }
                else
                {
                    WriteDebugLog("[needSave] 用户选择不保存，删除空白处方");
                    // 用户选择不保存，删除空白处方
                    this.ExcuteExcetionMethodChangeCursor(() =>
                    {
                        DelEmptyPresc();
                    });
                }
                return false;
            }
            else
            {
                this.ExcuteExcetionMethodChangeCursor(() =>
                {
                    DelEmptyPresc();
                });
            }
            return false;
        }
        /// <summary>
        /// 新增处方毁方
        /// </summary>
        private bool DelEmptyPresc()
        {
            #region 新方直接毁方
            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(BsPrescList);
            if (presc != null && Constants.NEW_ORDER_STATE_STR.Equals(presc.STATE))
            {
                GvPrestList.BeforeLeaveRow -= GvPrestList_BeforeLeaveRow;
                this.BsPrescList.Remove(presc);
                this.BsPrescList.ResetBindings(false);
                DelItem(presc);
                GvPrestList.BeforeLeaveRow += GvPrestList_BeforeLeaveRow;
                return true;
            }
            #endregion
            return false;
        }
        private void PrescFoucsRowChanged(int handle)
        {
            OUTP_ORDERS_STANDARD presc = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsPrescList);
            OUTP_ORDERS_STANDARD prescOrder = this.GvPrestList.GetRow(handle) as OUTP_ORDERS_STANDARD;
            if (prescOrder == null)
            {
                return;
            }
            this.lueDispensary.EditValue = prescOrder.PERFORMED_BY;
            bool isCanEditor = !prescOrder.STATE.Equals(Constants.NEW_ORDER_STATE_STR);
            if (isCanEditor)
            {
                DataTable dt_diag = new DataTable();
                DataColumn dc_diag = new DataColumn("DIAGNOSIS_DESC", typeof(string));
                dt_diag.Columns.Add(dc_diag);
                DataRow dr_diag = dt_diag.NewRow();
                dr_diag["DIAGNOSIS_DESC"] = prescOrder.DIAGNOSIS_DESC;
                dt_diag.Rows.Add(dr_diag);
                this.LuDiagDesc.Properties.DataSource = dt_diag;
            }
            else
            {
                this.LuDiagDesc.Properties.DataSource = this.OrderBusiness.GetDtDiagnosis();
            }
            this.LuDiagDesc.SetEditValue(prescOrder.DIAGNOSIS_DESC);
            this.TxtPrescAttr.EditValue = prescOrder.PRESC_ATTR ?? PrescParameter.PrescAttr;
            this.luePespDise.EditValue = prescOrder.OPSP_DISE_CODE;
            this.BsOrder.Clear();
            int insurCount = 0;
            this.Orders.ForEach(order =>
            {
                if (!string.IsNullOrWhiteSpace(prescOrder.APPOINT_NO) && prescOrder.APPOINT_NO.Equals(order.APPOINT_NO))
                {
                    if (order.INSUR_ADULT.Equals("0"))
                    {
                        insurCount++;
                    }
                    this.BsOrder.Add(order);
                }
            });

            List<OUTP_ORDERS_STANDARD> listOrder = this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
            PrescBusiness.SetSubPrescFlag(listOrder);
            BsOrder.Clear();
            BsOrder.DataSource = listOrder;
            this.SetEditorReadonly(isCanEditor);
            BsOrder.ResetBindings(false);
        }
        private void GvPrestList_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            this.ExcuteExcetionMethodChangeCursor(() =>
            {
                PrescFoucsRowChanged(e.FocusedRowHandle);
            });
        }

        private BindingSource BsOrderCosts { get; set; }
        private void BindOrderCosts()
        {
            this.BsOrderCosts.DataSource = GetEditOrder()?.OrderCosts;
        }
        private void GvOrders_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            BindOrderCosts();
        }

        private OUTP_ORDERS_COSTS_STANDARD GetEditOrderCosts()
        {
            OUTP_ORDERS_COSTS_STANDARD orderCosts = this.GetEditModel<OUTP_ORDERS_COSTS_STANDARD>(this.BsOrderCosts);
            return orderCosts;
        }

        private void RefreshOrderCosts()
        {
            this.BsOrderCosts.ResetBindings(false);
        }

        private void BsOrder_CurrentItemChanged(object sender, EventArgs e)
        {
            OUTP_ORDERS_COSTS_STANDARD orderCosts = GetEditOrderCosts();
            OUTP_ORDERS_STANDARD order = GetEditOrder();
            if (orderCosts == null || (order.ORDER_NO != orderCosts.ORDER_NO || order.ORDER_SUB_NO != orderCosts.ORDER_SUB_NO))
            {
                BindOrderCosts();
            }
            else
            {
                RefreshOrderCosts();
            }
        }

        private void toolStripMenuItem3_Click(object sender, EventArgs e)
        {
            Frmskintest frm = new Frmskintest();
            frm.ClinicNo = PatientInfo.CLINIC_NO;
            if (frm.ShowDialog() == DialogResult.OK)
            {
                List<OUTP_ORDERS_STANDARD> Orders = OrderBusiness.GetOrdersList();
                List<OUTP_ORDERS_COSTS_STANDARD> OrderCosts = OrderBusiness.GetOrderCostsList();
                OrderBusiness.BingOrdersCosts(Orders, OrderCosts);
                OrderBusiness.SetMaxOrderNo(Orders);
                PrescBusiness.SetSubPrescFlag(Orders);
                InitData(Orders, false);
            }
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            if (GvOrders.RowCount > 0)
            {
                OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);

                string DRUG_NAME = order.ORDER_TEXT.ToString();//药品名称
                string DRUG_CODE = order.ORDER_CODE.ToString();//药品ID
                string guige = order.ITEM_SPEC.ToString();//规格
                string changshang = order.FIRM_ID.ToString();//厂商
                try
                {
                    #region 合理用药 kangjia
                    if (PrescParameter.PassFirm.Equals("美康"))
                    {
                        PlatPublic.Common.MeiKang_Transfer.MDC_DoSetDrug(DRUG_CODE + "_" + guige + "_" + changshang, DRUG_NAME);
                        contextMenuStrip1.Visible = false;
                        PlatPublic.Common.MeiKang_Transfer.MDC_DoRefDrug(51);
                        contextMenuStrip1.Visible = true;
                    }

                    if (PrescParameter.PassFirm.Equals("壹途"))
                    {
                        instructionForm form = new instructionForm();
                        form.OpenInstruction("**********", "9095", DRUG_CODE);// + guige + changshang);
                    }
                    #endregion
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            if (GvOrders.RowCount > 0)
            {
                OUTP_ORDERS_STANDARD order = this.GetEditModel<OUTP_ORDERS_STANDARD>(this.BsOrder);

                string DRUG_NAME = order.ORDER_TEXT.ToString();//药品名称
                string DRUG_CODE = order.ORDER_CODE.ToString();//药品ID
                string guige = order.ITEM_SPEC.ToString();//规格
                string changshang = order.FIRM_ID.ToString();//厂商
                try
                {
                    #region 合理用药 kangjia
                    if (PrescParameter.PassFirm.Equals("美康"))
                    {
                        PlatPublic.Common.MeiKang_Transfer.MDC_DoSetDrug(DRUG_CODE + "_" + guige + "_" + changshang, DRUG_NAME);
                        contextMenuStrip1.Visible = false;
                        PlatPublic.Common.MeiKang_Transfer.MDC_DoRefDrug(11);
                        contextMenuStrip1.Visible = true;
                    }

                    if (PrescParameter.PassFirm.Equals("壹途"))
                    {
                        instructionForm form = new instructionForm();
                        form.OpenInstruction("**********", "9095", DRUG_CODE);// + guige + changshang);
                    }
                    #endregion
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void CalculateYJDX()
        {
            OUTP_ORDERS_STANDARD order = this.GetEditOrder();
            List<OUTP_ORDERS_STANDARD> addOrders = this.GetAddOrders();

            // 添加调试日志
            WriteDebugLog($"[UcDrugPresc.CalculateYJDX] 方法开始 - 药品名称: {order?.ORDER_TEXT}, 药品代码: {order?.ORDER_CODE}, 数量: {order?.AMOUNT}");

            if (order == null)
            {
                WriteDebugLog("[UcDrugPresc.CalculateYJDX] order为null，退出");
                return;
            }
            //如果没有医嘱类别，则是代表改记录是新增的空记录
            if (string.IsNullOrEmpty(order.ORDER_CLASS))
            {
                WriteDebugLog("[UcDrugPresc.CalculateYJDX] ORDER_CLASS为空，退出");
                return;
            }

            List<OUTP_ORDERS_STANDARD> removeOrderList = new List<OUTP_ORDERS_STANDARD>();
            List<OUTP_ORDERS_STANDARD> addOrderList = new List<OUTP_ORDERS_STANDARD>();
            string strMsg = "";

            // 修复：确保调用正确的WDrugPresc.GetAmountChangeOrders方法
            // 对于西药，应该调用OrderBusiness.wPresc而不是PrescBusiness
            bool result = false;
            if (order.ORDER_CLASS.Equals(OrderClassDict.ORDER_CLASS_WESTERN_MEDICINE))
            {
                // 西药：调用OrderBusiness.wPresc.GetAmountChangeOrders
                WriteDebugLog($"[UcDrugPresc.CalculateYJDX] 调用OrderBusiness.wPresc.GetAmountChangeOrders - 药品名称: {order.ORDER_TEXT}");
                result = this.OrderBusiness.wPresc.GetAmountChangeOrders(order, this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList(), addOrders, ref addOrderList, ref removeOrderList, ref strMsg);
            }
            else
            {
                // 其他类型：调用PrescBusiness.GetAmountChangeOrders
                WriteDebugLog($"[UcDrugPresc.CalculateYJDX] 调用PrescBusiness.GetAmountChangeOrders - 药品名称: {order.ORDER_TEXT}");
                result = PrescBusiness.GetAmountChangeOrders(order, this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList(), addOrders, ref addOrderList, ref removeOrderList, ref strMsg);
            }

            WriteDebugLog($"[UcDrugPresc.CalculateYJDX] GetAmountChangeOrders结果 - result: {result}, strMsg: {strMsg}");

            if (!result)
            {
                if(!string.IsNullOrEmpty(strMsg))
                {
                    WriteDebugLog($"[UcDrugPresc.CalculateYJDX] 显示错误消息: {strMsg}");
                    XtraMessageBox.Show(strMsg, "提示");
                    return;
                }
            }
            if ((addOrderList != null && addOrderList.Count >= 1) || (removeOrderList != null && removeOrderList.Count >= 1))
            {
                foreach (OUTP_ORDERS_STANDARD orderRemove in removeOrderList)
                {
                    this.BsOrder.Remove(orderRemove);
                    this.OrderBusiness.Remove(orderRemove);
                    addOrders.Remove(orderRemove);
                }
                foreach (OUTP_ORDERS_STANDARD order1 in addOrderList)
                {
                    OUTP_ORDERS_STANDARD order2 = this.OrderBusiness.Add(order1);

                    List<OUTP_ORDERS_STANDARD> ordersTemp = this.BsOrder.List.Cast<OUTP_ORDERS_STANDARD>().ToList();
                    int idex = BsOrder.IndexOf(ordersTemp.Find(r => r.ORDER_NO == order2.ORDER_NO + 1));
                    if (ordersTemp.Count == 0 && ordersTemp.Find(r => r.ORDER_NO == order2.ORDER_NO + 1) == null || idex < 0)
                        BsOrder.Add(order2);
                    else
                        this.BsOrder.Insert(BsOrder.IndexOf(ordersTemp.Find(r => r.ORDER_NO == order.ORDER_NO + 1)), order2);
                    //this.BsOrder.Add(order2);
                    PrescBusiness.CellValueChanged("AMOUNT", order2, this.GetAddOrders());
                }
            }
            else
            {
                PrescBusiness.CellValueChanged("AMOUNT", order, this.GetAddOrders());
            }
        }

        private void TxtPrescAttr_EditValueChanged(object sender, EventArgs e)
        {
            string prescAttr = "";
            if (TxtPrescAttr.EditValue != null && !string.IsNullOrEmpty(TxtPrescAttr.EditValue.ToString()))
                prescAttr = TxtPrescAttr.EditValue.ToString();
            if(prescAttr == "慢特处方")
            {
                luePespDise.Enabled = true;
                luePespDise.ReadOnly = false;
            }
            else
            {
                luePespDise.Enabled = false;
                luePespDise.EditValue = null;
            }
        }
    }
}
