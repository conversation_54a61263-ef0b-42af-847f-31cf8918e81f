﻿[2025-08-29 12:30:34.956] [PrescBusiness.SetClinciItem] 中药默认剂量 - 药品=砂仁, 使用默认剂量=5g（不使用DOSE_PER_UNIT=1.0）
[2025-08-29 12:30:35.286] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:30:35.288] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:30:35.288] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:30:35.288] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:30:35.301] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:30:58.577] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:30:58.577] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:30:58.578] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:30:58.578] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:30:58.578] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:30:58.598] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:30:58.599] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:30:58.599] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:30:58.599] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:30:58.599] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:30:58.768] [CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: 1
[2025-08-29 12:30:58.768] [CDrugPresc.AutoDividePresc] 分方前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:30:58.786] [CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: 1
[2025-08-29 12:30:58.787] [CDrugPresc.AutoDividePresc] 开始执行库存检查
[2025-08-29 12:30:58.944] [CDrugPresc.AutoDividePresc] 库存检查通过
[2025-08-29 12:30:59.044] [CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: 8135, 分配ORDER_NO: 4, 中药数量: 1
[2025-08-29 12:30:59.044] [CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1
[2025-08-29 12:30:59.044] [CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, 费用记录数量: 1
[2025-08-29 12:30:59.045] [CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: 砂仁, 原ORDER_NO: 3, 原ORDER_SUB_NO: 1, 原ITEM_NO: 1
[2025-08-29 12:30:59.045] [CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: 砂仁, 新ORDER_NO: 4, 新ORDER_SUB_NO: 1, 新费用ITEM_NO: 1, CLINIC_NO: 250717000ADMIN00001
[2025-08-29 12:30:59.045] [CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: 1
[2025-08-29 12:30:59.045] [CDrugPresc.AutoDividePresc] 分方后状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:30:59.046] [CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功
[2025-08-29 12:31:00.993] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 新方, 剂数: 1
[2025-08-29 12:31:00.993] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:31:00.994] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:01.150] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:31:01.150] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:01.151] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:31:01.151] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:31:01.152] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:01.152] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:01.153] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:01.153] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:31:01.254] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:01.683] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:31:01.683] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:01.683] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:31:01.684] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:31:01.684] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:31:01.780] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:01.863] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:31:01.864] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:01.864] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:31:01.864] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:31:01.865] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:01.865] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:01.865] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:01.865] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:31:01.922] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:02.379] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:31:02.379] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:02.380] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:31:02.380] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:31:02.380] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:02.381] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:02.381] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:02.381] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:31:02.441] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:02.593] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:31:02.593] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:02.594] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:31:02.594] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:31:02.595] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:02.595] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:31:02.595] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:02.595] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:31:02.596] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:23.286] [PrescBusiness.SetClinciItem] 中药默认剂量 - 药品=砂仁, 使用默认剂量=5g（不使用DOSE_PER_UNIT=1.0）
[2025-08-29 12:31:23.578] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:23.578] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:23.578] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:31:23.579] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:23.579] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:27.184] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:27.185] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:27.185] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:31:27.185] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:27.185] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:27.330] [CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: 1
[2025-08-29 12:31:27.331] [CDrugPresc.AutoDividePresc] 分方前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:31:27.341] [CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: 1
[2025-08-29 12:31:27.341] [CDrugPresc.AutoDividePresc] 开始执行库存检查
[2025-08-29 12:31:27.434] [CDrugPresc.AutoDividePresc] 库存检查通过
[2025-08-29 12:31:27.527] [CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: 8136, 分配ORDER_NO: 6, 中药数量: 1
[2025-08-29 12:31:27.527] [CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1
[2025-08-29 12:31:27.527] [CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, 费用记录数量: 1
[2025-08-29 12:31:27.528] [CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: 砂仁, 原ORDER_NO: 5, 原ORDER_SUB_NO: 1, 原ITEM_NO: 1
[2025-08-29 12:31:27.528] [CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: 砂仁, 新ORDER_NO: 6, 新ORDER_SUB_NO: 1, 新费用ITEM_NO: 1, CLINIC_NO: 250717000ADMIN00001
[2025-08-29 12:31:27.528] [CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: 1
[2025-08-29 12:31:27.528] [CDrugPresc.AutoDividePresc] 分方后状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:31:27.529] [CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功
[2025-08-29 12:31:29.486] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 新方, 剂数: 1
[2025-08-29 12:31:29.486] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:31:29.486] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:29.612] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8136, 剂数: 1
[2025-08-29 12:31:29.612] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:29.613] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8136
[2025-08-29 12:31:29.613] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:29.614] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:29.614] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:29.614] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:29.614] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:29.693] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:30.145] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:31:30.145] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:30.146] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:31:30.146] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:31:30.146] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:31:30.223] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:30.310] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8136, 剂数: 1
[2025-08-29 12:31:30.310] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:30.310] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8136
[2025-08-29 12:31:30.311] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:30.311] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:30.311] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:30.312] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:30.312] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:30.352] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:30.632] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8136, 剂数: 1
[2025-08-29 12:31:30.633] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:30.633] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8136
[2025-08-29 12:31:30.634] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:30.634] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:30.634] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:31:30.634] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:30.634] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:30.694] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:31:30.783] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8136, 剂数: 1
[2025-08-29 12:31:30.783] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:31:30.783] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8136
[2025-08-29 12:31:30.784] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:31:30.784] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:31:30.784] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:31:30.784] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:31:30.785] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:31:30.785] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:02.539] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8136, 剂数: 1
[2025-08-29 12:47:02.539] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:47:02.539] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:02.663] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:47:02.663] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:02.663] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:47:02.664] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:47:02.664] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:47:02.664] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:47:02.665] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:47:02.665] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:47:02.743] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:03.140] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:47:03.140] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:03.140] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:47:03.141] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:47:03.141] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:47:03.213] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:03.297] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:47:03.297] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:03.297] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8135
[2025-08-29 12:47:03.298] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:47:03.298] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:47:03.299] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:47:03.299] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:47:03.299] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:47:03.338] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:05.227] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8135, 剂数: 1
[2025-08-29 12:47:05.227] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:47:05.228] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:05.328] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:47:05.328] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:05.328] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:47:05.329] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:47:05.329] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:47:05.369] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:05.688] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:47:05.689] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:05.689] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:47:05.689] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:47:05.690] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:47:05.730] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:47:05.808] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:47:05.809] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:47:05.809] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:47:05.810] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:47:05.810] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:47:05.810] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:11.524] [PrescBusiness.SetClinciItem] 中药默认剂量 - 药品=砂仁, 使用默认剂量=5g（不使用DOSE_PER_UNIT=1.0）
[2025-08-29 12:48:11.847] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:11.849] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:11.850] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:11.850] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:11.850] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:11.873] [CDrugPresc.GetAmountChangeOrders] 中药跳过进定销处理 - 药品=砂仁
[2025-08-29 12:48:18.094] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:18.095] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:18.095] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:18.095] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:18.095] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:18.096] [CDrugPresc.GetAmountChangeOrders] 中药跳过进定销处理 - 药品=砂仁
[2025-08-29 12:48:18.117] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:18.118] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:18.118] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:18.118] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:18.118] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:18.300] [CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: 1
[2025-08-29 12:48:18.300] [CDrugPresc.AutoDividePresc] 分方前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:48:18.319] [CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: 1
[2025-08-29 12:48:18.319] [CDrugPresc.AutoDividePresc] 开始执行库存检查
[2025-08-29 12:48:18.451] [CDrugPresc.AutoDividePresc] 库存检查通过
[2025-08-29 12:48:18.553] [CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: 8137, 分配ORDER_NO: 4, 中药数量: 1
[2025-08-29 12:48:18.554] [CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1
[2025-08-29 12:48:18.555] [CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, 费用记录数量: 1
[2025-08-29 12:48:18.555] [CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: 砂仁, 原ORDER_NO: 3, 原ORDER_SUB_NO: 1, 原ITEM_NO: 1
[2025-08-29 12:48:18.558] [CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: 砂仁, 新ORDER_NO: 4, 新ORDER_SUB_NO: 1, 新费用ITEM_NO: 1, CLINIC_NO: 250717000ADMIN00001
[2025-08-29 12:48:18.558] [CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: 1
[2025-08-29 12:48:18.558] [CDrugPresc.AutoDividePresc] 分方后状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:48:18.558] [CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功
[2025-08-29 12:48:20.846] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 新方, 剂数: 1
[2025-08-29 12:48:20.846] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:48:20.846] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:21.000] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8137, 剂数: 1
[2025-08-29 12:48:21.000] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:21.000] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8137
[2025-08-29 12:48:21.001] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:21.001] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:21.002] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:21.002] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:21.002] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:21.110] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:21.623] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:48:21.623] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:21.624] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:48:21.624] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:48:21.625] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:48:21.712] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:21.798] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8137, 剂数: 1
[2025-08-29 12:48:21.798] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:21.798] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8137
[2025-08-29 12:48:21.799] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:21.799] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:21.799] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:21.800] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:21.800] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:21.872] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:22.332] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8137, 剂数: 1
[2025-08-29 12:48:22.332] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:22.333] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8137
[2025-08-29 12:48:22.333] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:22.333] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:22.334] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:22.334] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:22.334] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:22.394] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:22.482] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8137, 剂数: 1
[2025-08-29 12:48:22.482] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:22.482] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8137
[2025-08-29 12:48:22.483] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 10g
[2025-08-29 12:48:22.483] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:22.483] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 10g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:22.484] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 10g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:22.484] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 10g
[2025-08-29 12:48:22.484] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:29.039] [PrescBusiness.SetClinciItem] 中药默认剂量 - 药品=砂仁, 使用默认剂量=5g（不使用DOSE_PER_UNIT=1.0）
[2025-08-29 12:48:29.351] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:29.351] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:29.351] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:29.351] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:29.352] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:29.369] [CDrugPresc.GetAmountChangeOrders] 中药跳过进定销处理 - 药品=砂仁
[2025-08-29 12:48:32.982] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:32.982] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:32.982] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:32.983] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:32.983] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:33.123] [CDrugPresc.AutoDividePresc] 分方前状态 - 医嘱数量: 1
[2025-08-29 12:48:33.124] [CDrugPresc.AutoDividePresc] 分方前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:48:33.133] [CDrugPresc.AutoDividePresc] 检查点[开始分方] - AutoDetachDrug参数: 1
[2025-08-29 12:48:33.134] [CDrugPresc.AutoDividePresc] 开始执行库存检查
[2025-08-29 12:48:33.228] [CDrugPresc.AutoDividePresc] 库存检查通过
[2025-08-29 12:48:33.332] [CDrugPresc.AutoDividePresc] 检查点[处方分组] - 处方号: 8138, 分配ORDER_NO: 6, 中药数量: 1
[2025-08-29 12:48:33.332] [CDrugPresc.AutoDividePresc] 检查点[ORDER_SUB_NO分配] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1
[2025-08-29 12:48:33.332] [CDrugPresc.AutoDividePresc] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, 费用记录数量: 1
[2025-08-29 12:48:33.333] [CDrugPresc.AutoDividePresc] 检查点[费用记录修改前] - ORDER_TEXT: 砂仁, 原ORDER_NO: 5, 原ORDER_SUB_NO: 1, 原ITEM_NO: 1
[2025-08-29 12:48:33.333] [CDrugPresc.AutoDividePresc] 检查点[费用ITEM_NO分配] - ORDER_TEXT: 砂仁, 新ORDER_NO: 6, 新ORDER_SUB_NO: 1, 新费用ITEM_NO: 1, CLINIC_NO: 250717000ADMIN00001
[2025-08-29 12:48:33.333] [CDrugPresc.AutoDividePresc] 分方后状态 - 医嘱数量: 1
[2025-08-29 12:48:33.333] [CDrugPresc.AutoDividePresc] 分方后状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-29 12:48:33.334] [CDrugPresc.AutoDividePresc] 检查点[分方完成] - 成功
[2025-08-29 12:48:35.073] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 新方, 剂数: 1
[2025-08-29 12:48:35.073] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 0
[2025-08-29 12:48:35.073] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:35.195] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8138, 剂数: 1
[2025-08-29 12:48:35.196] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:35.196] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8138
[2025-08-29 12:48:35.197] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:35.197] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:35.197] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:35.197] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:35.198] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:35.283] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:35.741] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 6742, 剂数: 1
[2025-08-29 12:48:35.741] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:35.742] [CDrugPresc.RepetitionChanged] 处理药品: 枸杞子, 处方号: 6742
[2025-08-29 12:48:35.742] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 枸杞子, 药品代码: 63080241YP1, 进入时dosage: 10g
[2025-08-29 12:48:35.742] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 枸杞子, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-29 12:48:35.824] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:35.908] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8138, 剂数: 1
[2025-08-29 12:48:35.908] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:35.908] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8138
[2025-08-29 12:48:35.909] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:35.909] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:35.909] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:35.910] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:35.910] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:35.947] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:36.222] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8138, 剂数: 1
[2025-08-29 12:48:36.224] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:36.224] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8138
[2025-08-29 12:48:36.224] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:36.224] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:36.225] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 0
[2025-08-29 12:48:36.225] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:36.225] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:36.303] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:36.391] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8138, 剂数: 1
[2025-08-29 12:48:36.391] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 1
[2025-08-29 12:48:36.392] [CDrugPresc.RepetitionChanged] 处理药品: 砂仁, 处方号: 8138
[2025-08-29 12:48:36.392] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:36.392] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:36.393] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:36.393] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:36.393] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:36.394] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-29 12:48:54.942] [PrescBusiness.SetClinciItem] 中药默认剂量 - 药品=砂仁, 使用默认剂量=5g（不使用DOSE_PER_UNIT=1.0）
[2025-08-29 12:48:55.237] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:55.238] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:55.238] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:55.238] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:55.238] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:48:55.265] [CDrugPresc.GetAmountChangeOrders] 中药跳过进定销处理 - 药品=砂仁
[2025-08-29 12:48:59.955] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:48:59.955] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:48:59.955] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:48:59.955] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:48:59.956] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
[2025-08-29 12:49:10.695] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 砂仁, 药品代码: 63080238YP1, 进入时dosage: 5g
[2025-08-29 12:49:10.695] [PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: 0, 药品代码: 63080238YP1, 药品名称: 砂仁
[2025-08-29 12:49:10.695] [PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: 砂仁, 药品代码: 63080238YP1, 当前剂量: 5g, dosagePerUnit: 1.0, AMOUNT_PER_PACKAGE: 1
[2025-08-29 12:49:10.695] [PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: 砂仁, 药品代码: 63080238YP1, 保持原始剂量: 5g, 原因: dosagePerUnit=1且dosage>1，避免重新计算
[2025-08-29 12:49:10.696] [CDrugPresc.SetAmout] 检查点[正常计算] - 药品名称: 砂仁, CalculateSplitDosage返回: 5g
