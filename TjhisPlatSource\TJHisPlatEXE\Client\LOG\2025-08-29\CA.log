2025-08-29 13:04:46
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:46
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:47
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:47
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:48
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:48
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:49
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:49
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:50
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:50
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:51
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:51
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v79mew5koahzwbzk"}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v79mew5koahzwbzk","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v79mew5koahzwbzk",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，url:http://11.11.69.31:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，url:http://11.11.69.31:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"v168e0sa4kqthatl"}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"v168e0sa4kqthatl","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 13:04:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "v168e0sa4kqthatl",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
