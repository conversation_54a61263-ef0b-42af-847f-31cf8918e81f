﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Tjhis.Outpdoct.Station.Common;
using Tjhis.Outpdoct.Station.Model;
using PlatCommon.Common;

namespace Tjhis.Outpdoct.Station.Dal
{
    /// <summary>
    /// 药品信息 liujun add 2023-02-15
    /// </summary>
    public class DrugDictDal:CommonDal<DRUG_DICT>
    {
        protected override string SelectSql => @"SELECT DRUG_CODE,
       DRUG_NAME,
       DOSE_PER_UNIT,
       DOSE_UNITS,
       DRUG_SPEC,
       UNITS,
       PACKAGE_SPEC_1,
       PACKAGE_UNITS_1,
       PACKAGE_1,
       PACKAGE_SPEC_2,
       PACKAGE_UNITS_2,
       PACKAGE_2,
       DRUG_FORM,
       TOXI_PROPERTY,
       DRUG_INDICATOR,
       INPUT_CODE,
       INPUT_CODE_WB,
       OTC,
       LIMIT_CLASS,
       BASIC_DRUG_FLAG,
       EXPENSIVE_NAME,
       ANTIBIOTIC_FLAG,
       DDD,
       VOL_UNIT,
       VOLUM,
       WEIGHT_UNIT,
       WEIGHT,
       CONCENTRATION,
       PIVA_FLAG,
       PIVA_DOSAGE,
       PRICE_MODE,
       PRICE_RATIO,
       ENTERED_DATETIME,
       STOP_FLAG,
       TRANSFUSION_FLAG,
       DRUG_SAVEMODE,
       SPECIAL_DRUG,
       OUTP_PRINT_FLAG,
       PS_FLAG,
       TB_FLAG,
       DRUG_CLASS,
       UP_OLD,
       CLASS_NAME,
       MATERIAL_CODE,
       SHAD_FLAG,
       PRICE_CJ,
       PT_CODE,
       PT_NAME,
       DRUG_RATE_YN,
       DRUG_DM,
       DRUG_CONFIRM,
       BASE_FLAG,
       AUXILIARY_FLAG,
       ANTITUMOR_FLAG,
       SUB_DRUG_FORM,
       HIGH_DANGER,
       YPBM,
       ANTIBACTERIAL_FLAG,
       LICENSED
  FROM COMM.DRUG_DICT";

        /// <summary>
        /// 获取药品字典信息 liujun add 2023-02-16
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <returns></returns>
        public static DRUG_DICT GetDrugDictItem(string drugCode,string drugSpec)
        {
            try
            {
                string sql = "SELECT  * FROM  COMM.DRUG_DICT WHERE  DRUG_CODE = :DRUGCODE AND (DRUG_SPEC = :DRUGSPEC OR PACKAGE_SPEC_1 = :DRUGSPEC OR PACKAGE_SPEC_2 = :DRUGSPEC)";

                OracleDataHelper dal = new OracleDataHelper();
                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));
                parameters.Add(dal.CreateDbParameter(":DRUGSPEC", drugSpec));
                return dal.GetSingleModel<DRUG_DICT>(sql, parameters);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取drug_info的信息，主要用于皮试 liujun add 2023-02-16
        /// </summary>
        /// <param name="result"></param>
        /// <param name="hisUnitCode"></param>
        /// <returns></returns>
        public static DRUG_INFO GetDrugInfoItem(string drugCode)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT DRUG_CODE,DRUG_NAME,");
                builder.Append("DRUG_E_NAME,");
                builder.Append("ACTION,");
                builder.Append("INDICATION,");
                builder.Append("DOSAGE,");
                builder.Append("FORM,");
                builder.Append("PHARMACOKINETICS,");
                builder.Append("ADVERSE_REACTION,");
                builder.Append("ATTENTION,");
                builder.Append("CONTRAINDICATION,");
                builder.Append("SKINTEST, ");
                builder.Append("SKIN_TIME, ");
                builder.Append("SKIN_DRUG_CODE, ");
                builder.Append("SKIN_DRUG_NAME, ");
                builder.Append("BASIC_DRUG ");
                builder.Append(" FROM DRUG_INFO  ");
                builder.Append(" WHERE DRUG_CODE = :DRUGCODE ");
                builder.Append(" AND ROWNUM <= 1 ");
                
                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));

                string sql = builder.ToString();
                return dal.GetSingleModel<DRUG_INFO>(sql, parameters);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取drug_price_list信息
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="firmId"></param>
        /// <param name="units"></param>
        /// <returns></returns>
        public static DrugPriceListItem GetDrugPriceListItem(string drugCode,string drugSpec,string firmId,string units)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT a.FIRM_ID, ");
                builder.Append("a.DRUG_SPEC, ");
                builder.Append("a.UNITS, ");
                builder.Append("a.MIN_SPEC, ");
                builder.Append("a.AMOUNT_PER_PACKAGE, ");
                builder.Append("b.CLASS_ON_INP_RCPT, ");
                builder.Append("b.CLASS_ON_OUTP_RCPT, ");
                builder.Append("b.CLASS_ON_RECKONING, ");
                builder.Append("b.SUBJ_CODE, ");
                builder.Append("b.CLASS_ON_MR, ");
                builder.Append("a.MR_BILL_CLASS ");
                builder.Append("FROM DRUG_PRICE_MASTER_LIST a, DRUG_PRICE_DETAIL_LIST b ");
                builder.Append("WHERE a.DRUG_CODE = b.DRUG_CODE ");
                builder.Append("AND a.DRUG_SPEC = b.DRUG_SPEC ");
                builder.Append("AND a.FIRM_ID = b.FIRM_ID ");
                builder.Append("AND b.HIS_UNIT_CODE = :HISUNITCODE ");
                builder.Append("AND (b.STOP_DATE >= SYSDATE OR b.STOP_DATE IS NULL) ");
                builder.Append("AND b.START_DATE <= SYSDATE ");
                builder.Append(" AND a.UNITS = :UNITS");
                builder.Append(" AND a.FIRM_ID = :FIRMID");
                builder.Append(" AND a.DRUG_SPEC = :DRUGSPEC");
                builder.Append(" AND a.DRUG_CODE = :DRUGCODE");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":HISUNITCODE", PlatCommon.SysBase.SystemParm.HisUnitCode));
                parameters.Add(dal.CreateDbParameter(":UNITS", units));
                parameters.Add(dal.CreateDbParameter(":FIRMID", firmId));
                parameters.Add(dal.CreateDbParameter(":DRUGSPEC", drugSpec));
                parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));

                string sql = builder.ToString();
                return dal.GetSingleModel<DrugPriceListItem>(sql, parameters);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 查询药品库存 liujun add 2023-02-16
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="firmId"></param>
        /// <param name="dispensary"></param>
        /// <returns></returns>
        public static decimal GetDrugInventoryInStorage(string drugCode,string drugSpec,string firmId,string dispensary)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT SUM(QUANTITY) ");
                builder.Append(" FROM DRUG_STOCK  ");
                builder.Append(" WHERE DRUG_CODE = :DRUGCODE ");
                builder.Append(" AND PACKAGE_SPEC = :DRUGSPEC ");
                builder.Append(" AND FIRM_ID = :FRIMID");
                builder.Append(" AND STORAGE =:DISPENSARY");
                builder.Append(" AND  SUPPLY_INDICATOR = '1' ");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));
                parameters.Add(dal.CreateDbParameter(":DRUGSPEC", drugSpec));
                parameters.Add(dal.CreateDbParameter(":FRIMID", firmId));
                parameters.Add(dal.CreateDbParameter(":DISPENSARY", dispensary));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToDecimal(-999);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 查询待发药数量 liujun add 2023-3-10
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="firmId"></param>
        /// <param name="dispensary"></param>
        /// <returns></returns>
        public static decimal GetDrugInventoryInDispensing(string drugCode, string drugSpec, string firmId, string dispensary)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT SUM(nvl(QUANTITY,0)) ");
                builder.Append(" FROM DRUG_PRESC_MASTER_TEMP E, DRUG_PRESC_DETAIL_TEMP F  ");
                builder.Append(" WHERE E.PRESC_DATE = F.PRESC_DATE ");
                builder.Append(" AND E.PRESC_NO = F.PRESC_NO ");
                builder.Append(" AND E.DISPENSARY = :DISPENSARY ");
                builder.Append(" AND F.FIRM_ID = :FIRM_ID");
                builder.Append(" AND F.PACKAGE_SPEC = :PACKAGE_SPEC ");
                builder.Append(" AND F.DRUG_CODE = :DRUG_CODE");
                builder.Append($@" AND (SYSDATE - E.PRESC_DATE) <= (SELECT PARAMETER_VALUE FROM APP_CONFIGER_PARAMETER WHERE PARAMETER_NAME = 'DRUG_RESERVE_DAYS' AND APP_NAME = 'OUTPDOCT' AND HIS_UNIT_CODE ='{SystemParm.HisUnitCode}')" );

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DRUG_CODE", drugCode));
                parameters.Add(dal.CreateDbParameter(":PACKAGE_SPEC", drugSpec));
                parameters.Add(dal.CreateDbParameter(":FIRM_ID", firmId));
                parameters.Add(dal.CreateDbParameter(":DISPENSARY", dispensary));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToDecimal(0);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 查询待发药数量 liujun add 2023-3-10
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="firmId"></param>
        /// <param name="dispensary"></param>
        /// <returns></returns>
        public static decimal GetDrugInventoryInNotBilling(string drugCode, string drugSpec, string firmId, string dispensary)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT nvl(SUM(AMOUNT),0) ");
                //if (GlobalValue.DataStructVersion == DataStructVersion.V664)
                //{
                //    builder.Append(" FROM IND_OUTP_ORDERS_COSTS ");
                //}
                //else
                //{
                //    builder.Append(" FROM OUTP_ORDERS_COSTS  ");
                //}
                ////builder.Append(" WHERE BILL_DATE IS NULL ");
                ////builder.Append(" AND VISIT_DATE >= TRUNC(SYSDATE)");
                //builder.Append(" WHERE PERFORMED_BY = :PERFORMED_BY");
                //builder.Append(" AND ITEM_SPEC = :ITEM_SPEC ");
                //builder.Append(" AND ITEM_CODE = :ITEM_CODE ");

                //List<DbParameter> parameters = new List<DbParameter>();
                //parameters.Add(dal.CreateDbParameter(":ITEM_CODE", drugCode));
                //parameters.Add(dal.CreateDbParameter(":ITEM_SPEC", drugSpec + firmId));
                //parameters.Add(dal.CreateDbParameter(":PERFORMED_BY", dispensary));
                builder.Append(" from OUTP_ORDERS_STANDARD");
                builder.Append(" WHERE CHARGE_INDICATOR = 0 and ORDER_DATE>SYSDATE-1");
                builder.Append(" and PERFORMED_BY = :PERFORMED_BY");
                builder.Append(" and ORDER_CODE = :ORDER_CODE");
                builder.Append(" and FIRM_ID = :FIRM_ID");
                builder.Append(" and ITEM_SPEC = :ITEM_SPEC");
                //builder.Append(" and UNITS = :UNITS");
                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":PERFORMED_BY", dispensary));
                parameters.Add(dal.CreateDbParameter(":ORDER_CODE", drugCode));
                parameters.Add(dal.CreateDbParameter(":FIRM_ID", firmId));
                parameters.Add(dal.CreateDbParameter(":ITEM_SPEC", drugSpec));
                //parameters.Add(dal.CreateDbParameter(":UNITS", UNITS));
                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToDecimal(-999);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 查询药品的拆分属性 liujun add 2023-02-16
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="dispensary"></param>
        /// <returns></returns>
        public static string GetDrugSplitProperty(string drugCode, string dispensary)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT SEPARABLE ");
                builder.Append(" FROM DRUG_DISPENS_PROPERTY  ");
                builder.Append(" WHERE DRUG_CODE = :DRUGCODE ");
                builder.Append(" AND DISPENSARY =:DISPENSARY");
                builder.Append(" AND ROWNUM <= 1 ");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));
                parameters.Add(dal.CreateDbParameter(":DISPENSARY", dispensary));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToString("0");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取医生的处方权限 liujun add 2023-02-17
        /// </summary>
        /// <returns></returns>
        public static DataTable GetCurrentAuthorityForDrug()
        {
            try
            {
                using (OracleDataHelper dal = new OracleDataHelper())
                {
                    StringBuilder builder = new StringBuilder("SELECT nvl(PHDRUG_AUTHORITY,0) PHDRUG_AUTHORITY, ");
                    builder.Append(" nvl(CDRUG_AUTHORITY,0) CDRUG_AUTHORITY,  ");
                    builder.Append(" nvl(HMRUG_AUTHORITY,0) HMRUG_AUTHORITY,  ");
                    builder.Append(" nvl(LIMIT_CLASS_ASTRICT,0) LIMIT_CLASS_ASTRICT,  ");
                    builder.Append(" nvl(LIMIT_CLASS_UNRESTRICTED,0) LIMIT_CLASS_UNRESTRICTED,  ");
                    builder.Append(" nvl(LIMIT_CLASS_SPECIAL,0) LIMIT_CLASS_SPECIAL  ");
                    builder.Append(" FROM STAFF_DICT  ");
                    builder.Append(" WHERE USER_NAME = :USERNAME ");
                    builder.Append(" AND ROWNUM <= 1 ");

                    List<DbParameter> parameters = new List<DbParameter>();
                    parameters.Add(dal.CreateDbParameter(":USERNAME", PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME));
                    string sql = builder.ToString();

                    return dal.QueryList(sql, parameters, ""); 
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取公费用药目录里药品对应的医保等级 liujun add 2023-2-27
        /// </summary>
        /// <param name="chargeType"></param>
        /// <param name="drugCode"></param>
        /// <param name="minSpec"></param>
        /// <returns></returns>
        public static DataTable GetOfficialCatalogInfo(string chargeType,string drugCode,string minSpec)
        {
            try
            {

                using (OracleDataHelper dal = new OracleDataHelper())
                {
                    StringBuilder builder = new StringBuilder("SELECT A.CONSTRAINED_LEVEL, ");
                    builder.Append(" B.CLASS_NAME,  ");
                    builder.Append(" A.MEMO,  ");
                    builder.Append(" B.INSURANCE_FLAG  ");
                    builder.Append(" FROM OFFICIAL_DRUG_CATALOG A, OFFICIAL_DRUG_CATALOG_CLASS B  ");
                    builder.Append(" WHERE  A.CONSTRAINED_LEVEL = B.CLASS_CODE  ");
                    builder.Append(" AND CHARGE_TYPE = :CHARGETYPE  ");
                    builder.Append(" AND DRUG_CODE = :DRUGCODE  ");
                    builder.Append(" AND (DRUG_SPEC = :MINSPEC OR DRUG_SPEC = '*')  ");

                    List<DbParameter> parameters = new List<DbParameter>();
                    parameters.Add(dal.CreateDbParameter(":CHARGETYPE", chargeType));
                    parameters.Add(dal.CreateDbParameter(":DRUGCODE", drugCode));
                    parameters.Add(dal.CreateDbParameter(":MINSPEC", minSpec));
                    string sql = builder.ToString();
                    return dal.QueryList(sql, parameters, "");
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 查询指定毒属性的附属信息 liujun add 2023-2-27
        /// </summary>
        /// <param name="toxiProperty"></param>
        /// <returns></returns>
        public static DataTable GetToxiPropertyInfo(string toxiProperty)
        {
            try
            {
                using (OracleDataHelper dal = new OracleDataHelper())
                {
                    StringBuilder builder = new StringBuilder("SELECT ");
                    builder.Append(" PRESC_ATTR_NAME ");
                    builder.Append(" FROM DRUG_TOXI_PROPERTY_DICT  ");
                    builder.Append(" WHERE toxi_name = :TOXINAME  ");
                    builder.Append(" AND ROWNUM<= 1  ");

                    List<DbParameter> parameters = new List<DbParameter>();
                    parameters.Add(dal.CreateDbParameter(":TOXINAME", toxiProperty));
                    string sql = builder.ToString();

                    return dal.QueryList(sql, parameters, ""); 
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取药品对应的途径信息 liujun add 2023-3-6
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="adminName"></param>
        /// <returns></returns>
        public static DataTable GetAdminstrationForDrug(string drugCode,string drugSpec,string adminName)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                List<DbParameter> parameters = new List<DbParameter>();
                StringBuilder builder = new StringBuilder("");
                if (CommFunc.StrEmpty(drugCode) || CommFunc.StrEmpty(drugSpec))
                {
                    builder.Append(" SELECT ADMINISTRATION_NAME,");
                    builder.Append(" SERIAL_NO,");
                    builder.Append(" cast('__' AS varchar(20)) DRUG_CODE,");
                    builder.Append(" cast('__' AS varchar(20)) DRUG_SPEC,");
                    builder.Append(" cast('' AS varchar(8)) DOSE_UNITS,");
                    builder.Append(" cast(0 AS number(8, 3)) DOSE_PER_UNIT,");
                    builder.Append(" cast(0 AS number(12, 2)) MAX_DOSAGE,");
                    builder.Append(" cast('' AS varchar(16)) ADMINISTRATION,");
                    builder.Append(" cast(0 AS number(2)) FREQ_COUNTER,");
                    builder.Append(" cast(0 AS number(2)) FREQ_INTERVAL,");
                    builder.Append(" cast('' AS varchar(4)) FREQ_INTERVAL_UNIT,");
                    builder.Append(" cast('' AS varchar(16)) FREQUENCY,");
                    builder.Append(" cast(0 AS number(12, 2)) MAX_PRESC_DOSAGE,");
                    builder.Append(" cast(0 AS number(3)) MAX_OUTP_ABIDANCE,");
                    builder.Append(" cast('' AS varchar(20)) DECOCTION_PROPERTY,");
                    builder.Append(" cast('' AS varchar(100)) DOCTOR_MEMOS,");
                    builder.Append(" cast(0 AS number(13, 3)) NORMAL_DOSAGE,");
                    builder.Append(" cast(0 AS number(6)) DRUG_INDEX,");
                    builder.Append(" CDRUG_USING_FLAG");
                    builder.Append(" FROM ADMINISTRATION_DICT");
                    builder.Append(" WHERE ADMINISTRATION_NAME = :ADMINISTRATION_NAME");
                    builder.Append(" ORDER BY SERIAL_NO ASC");
                    parameters.Add(dal.CreateDbParameter(":ADMINISTRATION_NAME", adminName));
                    string sql = builder.ToString();
                    return dal.QueryList(sql, parameters, "");
                }
                else
                {
                    builder.Append(" SELECT a.DRUG_CODE,");
                    builder.Append(" a.DRUG_SPEC,");
                    builder.Append(" a.DOSE_UNITS,");
                    builder.Append(" a.DOSE_PER_UNIT,");
                    builder.Append(" a.MAX_DOSAGE,");
                    builder.Append(" a.ADMINISTRATION,");
                    builder.Append(" a.FREQ_COUNTER,");
                    builder.Append(" a.FREQ_INTERVAL,");
                    builder.Append(" a.FREQ_INTERVAL_UNIT,");
                    builder.Append(" a.FREQUENCY,");
                    builder.Append(" a.MAX_PRESC_DOSAGE,");
                    builder.Append(" a.MAX_OUTP_ABIDANCE,");
                    builder.Append(" a.DECOCTION_PROPERTY,");
                    builder.Append(" a.DOCTOR_MEMOS,");
                    builder.Append(" a.NORMAL_DOSAGE,");
                    builder.Append(" a.DRUG_INDEX,");
                    builder.Append(" b.ADMINISTRATION_NAME");
                    builder.Append(" FROM DRUG_RATIONAL_DOSAGE a, ADMINISTRATION_DICT b");
                    builder.Append(" WHERE a.ADMINISTRATION = b.ADMINISTRATION_CODE(+)");
                    builder.Append(" AND a.DRUG_CODE = :DRUG_CODE");
                    builder.Append(" AND a.DRUG_SPEC = :DRUG_SPEC");
                    builder.Append(" AND a.ADMINISTRATION = :ADMINISTRATION");
                    builder.Append(" ORDER BY a.DRUG_CODE ASC, ");
                    builder.Append(" a.DRUG_SPEC ASC, ");
                    builder.Append(" a.DRUG_INDEX ASC");
                    parameters.Add(dal.CreateDbParameter(":ADMINISTRATION", adminName));
                    parameters.Add(dal.CreateDbParameter(":DRUG_CODE", drugCode));
                    parameters.Add(dal.CreateDbParameter(":DRUG_SPEC", drugSpec));

                    string sql = builder.ToString();
                    DataTable table = dal.QueryList(sql, parameters, "");

                    if (table != null && table.Rows.Count > 0)
                    {
                        return table;
                    }

                    builder.Clear();
                    builder.Append(" SELECT ADMINISTRATION_NAME,");
                    builder.Append(" SERIAL_NO,");
                    builder.Append(" cast('__' AS varchar(20)) DRUG_CODE,");
                    builder.Append(" cast('__' AS varchar(20)) DRUG_SPEC,");
                    builder.Append(" cast('' AS varchar(8)) DOSE_UNITS,");
                    builder.Append(" cast(0 AS number(8, 3)) DOSE_PER_UNIT,");
                    builder.Append(" cast(0 AS number(12, 2)) MAX_DOSAGE,");
                    builder.Append(" cast('' AS varchar(16)) ADMINISTRATION,");
                    builder.Append(" cast(0 AS number(2)) FREQ_COUNTER,");
                    builder.Append(" cast(0 AS number(2)) FREQ_INTERVAL,");
                    builder.Append(" cast('' AS varchar(4)) FREQ_INTERVAL_UNIT,");
                    builder.Append(" cast('' AS varchar(16)) FREQUENCY,");
                    builder.Append(" cast(0 AS number(12, 2)) MAX_PRESC_DOSAGE,");
                    builder.Append(" cast(0 AS number(3)) MAX_OUTP_ABIDANCE,");
                    builder.Append(" cast('' AS varchar(20)) DECOCTION_PROPERTY,");
                    builder.Append(" cast('' AS varchar(100)) DOCTOR_MEMOS,");
                    builder.Append(" cast(0 AS number(13, 3)) NORMAL_DOSAGE,");
                    builder.Append(" cast(0 AS number(6)) DRUG_INDEX,");
                    builder.Append(" CDRUG_USING_FLAG");
                    builder.Append(" FROM ADMINISTRATION_DICT");
                    builder.Append(" WHERE ADMINISTRATION_NAME = :ADMINISTRATION_NAME");
                    builder.Append(" ORDER BY SERIAL_NO ASC");
                    parameters.Clear();
                    parameters.Add(dal.CreateDbParameter(":ADMINISTRATION_NAME", adminName));
                    sql = builder.ToString();
                    return dal.QueryList(sql, parameters, "");
                }


            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取指定的频次信息 liujun add 2023-3-7
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static DataTable GetPerformFreqDictItem(string name)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                List<DbParameter> parameters = new List<DbParameter>();
                StringBuilder builder = new StringBuilder("SELECT FREQ_COUNTER,");
                builder.Append(" FREQ_INTERVAL,");
                builder.Append(" FREQ_INTERVAL_UNITS");
                builder.Append(" FROM PERFORM_FREQ_DICT");
                builder.Append(" WHERE FREQ_DESC = :FREQ_DESC");
                parameters.Add(dal.CreateDbParameter(":FREQ_DESC", name));
                string sql = builder.ToString();
                return dal.QueryList(sql, parameters, "");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取指定科室的科室名称 liujun add 2023-3-8
        /// </summary>
        /// <param name="deptCode"></param>
        /// <returns></returns>
        public static string GetDeptName(string deptCode)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT DEPT_NAME ");
                builder.Append(" FROM DEPT_DICT  ");
                builder.Append(" WHERE DEPT_CODE = :DEPT_CODE ");
                builder.Append(" AND HIS_UNIT_CODE =:HIS_UNIT_CODE");
                builder.Append(" AND ROWNUM <= 1 ");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter(":DEPT_CODE", deptCode));
                parameters.Add(dal.CreateDbParameter(":HIS_UNIT_CODE", PlatCommon.SysBase.SystemParm.HisUnitCode));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToString("0");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取指定药品的包装数量 liujun add 2023-3-15
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="units"></param>
        /// <param name="firmId"></param>
        /// <returns></returns>
        public static decimal GetDrugAmountPerPackage(string drugCode, string drugSpec, string units, string firmId)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT AMOUNT_PER_PACKAGE ");
                builder.Append(" FROM DRUG_PRICE_MASTER_LIST a, DRUG_PRICE_DETAIL_LIST b ");
                builder.Append(" WHERE a.DRUG_CODE = b.DRUG_CODE AND ");
                builder.Append(" a.DRUG_SPEC = b.DRUG_SPEC AND ");
                builder.Append(" a.FIRM_ID = b.FIRM_ID AND ");
                builder.Append(" a.DRUG_CODE = :DRUG_CODE  AND  ");
                builder.Append(" a.DRUG_SPEC =:DRUG_SPEC AND ");
                builder.Append(" a.UNITS =:UNITS AND ");
                builder.Append(" a.FIRM_ID =:FIRM_ID AND ");
                builder.Append(" b.HIS_UNIT_CODE =:HIS_UNIT_CODE AND ");
                builder.Append(" b.START_DATE <= SYSDATE AND ");
                builder.Append(" (b.STOP_DATE IS NULL OR b.STOP_DATE > SYSDATE) AND ");
                builder.Append(" ROWNUM <= 1 ");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter("DRUG_CODE", drugCode));
                parameters.Add(dal.CreateDbParameter("DRUG_SPEC", drugSpec));
                parameters.Add(dal.CreateDbParameter("UNITS", units));
                parameters.Add(dal.CreateDbParameter("FIRM_ID", firmId));
                parameters.Add(dal.CreateDbParameter("HIS_UNIT_CODE", PlatCommon.SysBase.SystemParm.HisUnitCode));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToDecimal(0);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 获取指定药品最小单位剂量 liujun add 2023-3-15
        /// </summary>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <returns></returns>
        public static decimal GetDrugDosePerUnit(string drugCode, string drugSpec)
        {
            try
            {
                OracleDataHelper dal = new OracleDataHelper();
                StringBuilder builder = new StringBuilder("SELECT C.DOSE_PER_UNIT ");
                builder.Append(" FROM DRUG_STOCK A, DRUG_DICT C  ");
                builder.Append(" WHERE A.DRUG_SPEC = C.DRUG_SPEC AND A.DRUG_CODE = C.DRUG_CODE ");
                builder.Append(" AND A.DRUG_CODE = :DRUG_CODE");
                builder.Append(" AND A.PACKAGE_SPEC =:PACKAGE_SPEC");
                builder.Append(" AND A.HIS_UNIT_CODE =:HIS_UNIT_CODE");
                builder.Append(" AND ROWNUM <= 1 ");

                List<DbParameter> parameters = new List<DbParameter>();
                parameters.Add(dal.CreateDbParameter("DRUG_CODE", drugCode));
                parameters.Add(dal.CreateDbParameter("PACKAGE_SPEC", drugSpec));
                parameters.Add(dal.CreateDbParameter("HIS_UNIT_CODE", PlatCommon.SysBase.SystemParm.HisUnitCode));

                string sql = builder.ToString();
                return dal.GetSingleValue(sql, parameters).ToDecimal(0);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
