﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Tjhis.EmrOutp.Write.Comm.Globals;
using Tjhis.Outpdoct.Station.Common;
using Tjhis.Outpdoct.Station.Dal;
using Tjhis.Outpdoct.Station.Interface;
using Tjhis.Outpdoct.Station.Maintain;
using Tjhis.Outpdoct.Station.Model;
using Tjhis.Outpdoct.Station.Views.Presc;
using PlatCommon.Common;

namespace Tjhis.Outpdoct.Station.Business
{
    public class PrescBusiness:OrderApply
    {
        protected DrugDictDal DrugDictDal = new DrugDictDal();

        // 引用CDrugPresc中的静态inputResult集合
        protected static List<InputResult> inputResult => CDrugPresc.inputResult;
        public PrescBusiness(IOrders OrderBusiness) : base(OrderBusiness)
        {
        }
        /// <summary>
        /// 根据指定行药品拆分标志，计算药品实际单次消耗量：
        ///	如果药品拆包，则消耗量为实际填写单次剂量，否则，单次消耗量为满足单次剂量的最小药品包装剂量
        ///	liujun add 2023-3-13
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        protected decimal CalculateSplitDosage(OUTP_ORDERS_STANDARD row)
        {
            decimal dosage = 0;
            int splitFlag = row.SPLIT_FLAG.ToInt(0);

            // 添加方法入口日志
            WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 方法开始 - splitFlag: {splitFlag}, 药品代码: {row.ORDER_CODE}, 药品名称: {row.ORDER_TEXT}");

            if (splitFlag.Equals(1))
            {
                dosage = row.DOSAGE.ToDecimal(0);
                WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 拆包用药 - 直接返回dosage: {dosage}g");
            }
            else
            {
                string drugCode = row.ORDER_CODE.ToString("");
                string drugName = row.ORDER_TEXT.ToString("");
                string drugSpec = row.ITEM_SPEC.ToString("");
                string firmId = row.FIRM_ID.ToString("");
                string units = row.UNITS.ToString("");
                decimal dosagePerUnit = row.DOSE_PER_UNIT.ToDecimal(0);
                dosage = row.DOSAGE.ToDecimal(0);

                // 修复：保护原始dosage值，避免重新计算导致药品重量被错误修改

                // 添加调试日志：记录方法调用
                WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 方法调用 - 药品名称: {drugName}, 药品代码: {drugCode}, 当前剂量: {dosage}g, dosagePerUnit: {dosagePerUnit}, AMOUNT_PER_PACKAGE: {row.AMOUNT_PER_PACKAGE.ToDecimal(0)}");

                // 修复策略：如果dosage已经是合理值且dosagePerUnit=1，直接返回原始dosage
                // 这样可以避免不必要的重新计算，特别是对于复制的历史处方
                if (dosagePerUnit == 1.0m && dosage > 0 && dosage != 1.0m)
                {
                    WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 检查点[保护原始剂量] - 药品名称: {drugName}, 药品代码: {drugCode}, 保持原始剂量: {dosage}g, 原因: dosagePerUnit=1且dosage>1，避免重新计算");
                    return dosage;
                }

                // 对于dosage=1的情况，也需要特别处理，可能是被错误重置的
                if (dosage == 1.0m && dosagePerUnit == 1.0m)
                {
                    WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 检查点[可能的错误重置] - 药品名称: {drugName}, 药品代码: {drugCode}, 当前剂量: {dosage}g, 可能已被错误重置");
                }

                if (dosagePerUnit.Equals(0))
                {
                    DrugPriceListItem item = DrugDictDal.GetDrugPriceListItem(drugCode, drugSpec, units, firmId);
                    DRUG_DICT drugDict = DrugDictDal.GetDrugDictItem(drugCode, drugSpec);
                    decimal amountPerPackage = item.AMOUNT_PER_PACKAGE;
                    decimal doscPerUnit = drugDict.DOSE_PER_UNIT ?? 0;
                    if (doscPerUnit.Equals(0))
                        doscPerUnit = dosage;

                    dosagePerUnit = doscPerUnit;
                    row.DOSE_PER_UNIT = doscPerUnit;
                    row.AMOUNT_PER_PACKAGE = amountPerPackage;
                }

                if (dosagePerUnit.Equals(0))
                {
                    dosage = 0;
                    WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] dosagePerUnit为0，设置dosage为0");
                }
                else
                {
                    decimal originalDosage = dosage;
                    dosage = dosagePerUnit * Math.Ceiling(Math.Round(dosage / dosagePerUnit, 4));
                    WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 重新计算dosage - 原值: {originalDosage}g, 新值: {dosage}g, dosagePerUnit: {dosagePerUnit}");
                }

            }

            WriteDebugLog($"[PrescBusiness.CalculateSplitDosage] 方法结束 - 最终返回dosage: {dosage}g");
            return dosage;
        }
        /// <summary>
        /// 根据指定行药品拆分标志，计算药品实际单次消耗量：
        ///	如果药品拆包，则消耗量为实际填写单次剂量，否则，单次消耗量为满足单次剂量的最小药品包装剂量
        ///	liujun add 2023-3-13
        /// </summary>
        /// <param name="splitFlag"></param>
        /// <param name="drugCode"></param>
        /// <param name="drugSpec"></param>
        /// <param name="units"></param>
        /// <param name="firmId"></param>
        /// <param name="doscPerUnit"></param>
        /// <param name="amountPerPackage"></param>
        /// <param name="inDosage"></param>
        /// <returns></returns>
        protected decimal CalculateSplitDosage(int splitFlag, string drugCode, string drugSpec, string units, string firmId, ref decimal doscPerUnit, ref decimal amountPerPackage, decimal inDosage)
        {
            decimal dosage = 0;
            if (splitFlag.Equals(0))
            {
                // 若未记录药品最小包装剂量，则重新获取
                if (doscPerUnit.Equals(0))
                {
                    DrugPriceListItem item = DrugDictDal.GetDrugPriceListItem(drugCode, drugSpec, units, firmId);
                    DRUG_DICT drugDict = DrugDictDal.GetDrugDictItem(drugCode, drugSpec);
                    decimal mountPerPackage = item.AMOUNT_PER_PACKAGE;
                    doscPerUnit = drugDict.DOSE_PER_UNIT ?? 0;
                    if (doscPerUnit.Equals(0))
                        doscPerUnit = inDosage;
                }

                dosage = doscPerUnit * Math.Ceiling(Math.Round(inDosage / doscPerUnit, 4));
            }
            else
            {
                dosage = inDosage;
            }

            return dosage;
        }
        /// <summary>
        /// 计算执行次数、用药天数 描述: 根据药品数量、用法自动关计算用药天数 liujun add 2023-2-22
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="amountPerPackage"></param>
        /// <param name="doscPerUnit"></param>
        /// <param name="dosage"></param>
        /// <param name="frequnits"></param>
        /// <param name="freqinterval"></param>
        /// <param name="freqcounter"></param>
        /// <param name="splitFlag"></param>
        /// <param name="performTimes"></param>
        /// <param name="abidance"></param>
        /// <param name="insurAbidance"></param>
        protected void CalculateInsurAbidance(decimal amount, decimal amountPerPackage, decimal doscPerUnit, decimal dosage, string frequnits, int freqinterval, int freqcounter, string splitFlag, ref decimal performTimes, ref decimal abidance, ref decimal insurAbidance)
        {
            decimal days = 1;
            // 计算总执行次数（向下取整）
            if (amountPerPackage == 1 && (string.IsNullOrEmpty(splitFlag) || splitFlag.Equals("0")) && doscPerUnit >= dosage)
            {
                performTimes = Convert.ToInt32(Math.Round((amount), 4));
            }
            else
            {
                performTimes = Convert.ToInt32(Math.Round((amount * amountPerPackage * doscPerUnit / dosage), 4));
            }
            // 根据频次信息，计算用药天数
            if (!string.IsNullOrEmpty(frequnits))
            {
                if (freqcounter == 0 || freqinterval == 0)
                {
                    abidance = 1;
                    insurAbidance = 1;
                }
                else
                {
                    switch (frequnits)
                    {
                        case "周":
                            days = (performTimes * 7 * freqinterval) / freqcounter;
                            // 医保用药天数向下取整
                            insurAbidance = Convert.ToInt32(Math.Round(((amount * amountPerPackage * doscPerUnit / dosage) * 7 * freqinterval) / freqcounter, 4));
                            break;
                        case "日":
                            days = (performTimes * freqinterval) / freqcounter;
                            insurAbidance = Convert.ToInt32(Math.Round(((amount * amountPerPackage * doscPerUnit / dosage) * freqinterval) / freqcounter, 4));
                            break;
                        case "小时":
                            days = (performTimes * freqinterval) / (freqcounter * 24);
                            insurAbidance = Convert.ToInt32(Math.Round(((amount * amountPerPackage * doscPerUnit / dosage) * freqinterval) / (freqcounter * 24), 4));
                            break;
                        case "月":
                            days = (performTimes * 30 * freqinterval) / freqcounter;
                            insurAbidance = Convert.ToInt32(Math.Round(((amount * amountPerPackage * doscPerUnit / dosage) * 30 * freqinterval) / freqcounter, 4));
                            break;
                    }
                }
                // 考虑到隔日、间隔用药的情况，需要将频次系数重新整理：小于1时取1，否则向上取整
                abidance = Math.Ceiling(Math.Round(days, 4));
            }
            else
            {
                abidance = 1;
                insurAbidance = 1;
            }
            if (insurAbidance <= 0)
            {
                insurAbidance = 1;
            }
        }

        /// <summary>
        /// 单次用量校验 liujun add 2023-3-7
        /// </summary>
        /// <param name="row"></param>
        /// <param name="inAmount"></param>
        /// <param name="inDosage"></param>
        /// <param name="inFrequency"></param>
        /// <param name="inAbidance"></param>
        /// <param name="inPerformTimes"></param>
        /// <returns></returns>
        protected bool CheckDosageForDrug(OUTP_ORDERS_STANDARD row, decimal inAmount, decimal inDosage, string inFrequency, int inAbidance, int inPerformTimes)
        {
            if (row == null)
            {
                throw new MessageException("没有获取到当前数据行！");
            }
            string drugCode = row.ORDER_CODE.ToString("");
            string minSpec = row.MIN_SPEC.ToString("");
            string drugSpec = row.ITEM_SPEC.ToString("");
            string drugName = row.ORDER_TEXT.ToString("");
            string firmId = row.FIRM_ID.ToString("");
            string units = row.UNITS.ToString("");
            string administration = row.ADMINISTRATION.ToString("");
            int freqCounter = row.FREQ_COUNTER.ToInt(0);
            int freqInterval = row.FREQ_INTERVAL.ToInt(0);
            string freqIntervalUnits = row.FREQ_INTERVAL_UNIT.ToString("");
            int abidance = row.ABIDANCE.ToInt(0);
            decimal dosage = row.DOSAGE.ToDecimal(0);
            decimal amount = row.AMOUNT.ToDecimal(0);//数量
            decimal doscPerUnit = row.DOSE_PER_UNIT.ToDecimal(0); //最小单位剂量
            decimal amountPerPackage = row.AMOUNT_PER_PACKAGE.ToDecimal(0); //包装数量
            int splitFlag = row.SPLIT_FLAG.ToInt(0); //拆包装标志
            string mess = "";
            decimal performTimes = 0;
            int times = 0;
            dosage = row.DOSAGE.ToDecimal(0);
            if (CommFunc.StrEmpty(drugCode) || CommFunc.StrEmpty(minSpec) || CommFunc.StrEmpty(administration) || freqCounter <= 0 || doscPerUnit <= 0 || amountPerPackage <= 0)
                return true;

            DataTable table = DrugDictDal.GetAdminstrationForDrug(drugCode, minSpec, administration);
            if (table == null || table.Rows.Count == 0 || table.Rows[0]["DRUG_CODE"].ToString("") == "__")
                return true;

            decimal maxDosage = table.Rows[0]["MAX_DOSAGE"].ToDecimal(0); //单日最大用量
            decimal maxPrescDosage = table.Rows[0]["MAX_PRESC_DOSAGE"].ToDecimal(0); //单处方最大开药量
            int maxOutpAbidance = table.Rows[0]["MAX_OUTP_ABIDANCE"].ToInt(0); //门诊处方最大用药天数

            if (inAmount > 0)
            {
                amount = inAmount;

                dosage = this.CalculateSplitDosage(splitFlag, drugCode, drugSpec, units, firmId, ref doscPerUnit, ref amountPerPackage, dosage);
                if (dosage.Equals(0))
                {
                    performTimes = 0;
                }
                else
                {
                    performTimes = ((int)Math.Round(amount * amountPerPackage * doscPerUnit / dosage, 4)).ToDecimal();
                }

                if (!CommFunc.StrEmpty(freqIntervalUnits))
                {
                    if (freqCounter.Equals(0) || freqInterval.Equals(0))
                    {
                        abidance = 1;
                    }
                    else
                    {
                        switch (freqIntervalUnits)
                        {
                            case "周":
                                abidance = Math.Ceiling((performTimes * 7 * freqInterval) / freqCounter).ToInt(0);
                                break;

                            case "日":
                                abidance = Math.Ceiling((performTimes * freqInterval) / freqCounter).ToInt(0);
                                break;

                            case "小时":
                                abidance = Math.Ceiling((performTimes * freqInterval) / (freqCounter * 24)).ToInt(0);
                                break;
                        }
                    }
                }
                else
                {
                    abidance = 1;
                }
            }
            else if (inDosage > 0)
            {
                amount = inAmount;
                dosage = this.CalculateSplitDosage(splitFlag, drugCode, drugSpec, units, firmId, ref doscPerUnit, ref amountPerPackage, dosage);
            }
            else if (!CommFunc.StrEmpty(inFrequency))
            {
                CommFunc.SetDtToNull(table);
                table = DrugDictDal.GetPerformFreqDictItem(inFrequency);
                if (!table.Rows.Count.Equals(0))
                {
                    freqCounter = table.Rows[0]["FREQ_COUNTER"].ToInt(0);
                    freqInterval = table.Rows[0]["FREQ_INTERVAL"].ToInt(0);
                    freqIntervalUnits = table.Rows[0]["FREQ_INTERVAL_UNITS"].ToString("");
                }
                dosage = this.CalculateSplitDosage(splitFlag, drugCode, drugSpec, units, firmId, ref doscPerUnit, ref amountPerPackage, dosage);
            }
            else if (inAbidance > 0)
            {
                abidance = inAbidance;
                dosage = this.CalculateSplitDosage(splitFlag, drugCode, drugSpec, units, firmId, ref doscPerUnit, ref amountPerPackage, dosage);
            }
            else if (inPerformTimes > 0)
            {
                times = inPerformTimes;
                dosage = this.CalculateSplitDosage(splitFlag, drugCode, drugSpec, units, firmId, ref doscPerUnit, ref amountPerPackage, dosage);
            }
            else
            {
                return true;
            }

            //单日最大用量比较
            //单日最大用量=单次用药量*频次
            if (administration.IndexOf("外用").Equals(-1))
            {
                if (maxDosage > 0 && Math.Round(freqCounter * dosage, 3) > Math.Round(maxDosage, 3))
                    mess += "药品【" + drugName + "】途径为【" + administration + "】时，建议的单日最大用量为" + Math.Round(maxDosage, 3) + "，您所开的单日最大用量为，" + Math.Round(freqCounter * dosage, 3) + "(单次用量 " + Math.Round(dosage, 3) + "  X 单日执行次数 " + freqCounter + ")。" + "\r\n";
            }

            //用药天数比较 
            if (maxOutpAbidance > 0 && abidance > maxOutpAbidance)
            {
                if (CommFunc.StrEmpty(mess))
                {
                    mess += "药品【" + drugName + "】途径为【" + administration + "】时，建议的用药天数最大为" + maxOutpAbidance + "，您所开的用药天数为" + abidance + "。\r\n";
                }
                else
                {
                    mess += "建议的用药天数最大为" + maxOutpAbidance + "，您所开的用药天数为" + abidance + "。\r\n";
                }
            }

            if (CommFunc.StrEmpty(mess))
            {
                return true;
            }
            else
            {
                if (XtraMessageBox.Show(mess, "提示", MessageBoxButtons.OKCancel) == DialogResult.Yes)
                {
                    return true;
                }

                return false;
            }

        }

        /// <summary>
        /// 处方加药处理，现在处方的规是只有新增没有修改（保存过的） liujun add 2023-2-24
        /// </summary>
        /// <param name="result"></param>
        /// <param name="row"></param>
        /// <param name="dts"></param>
        /// <returns></returns>
        protected bool AddDrugBase(OutpPatientInfo pi, InputResult result, OUTP_ORDERS_STANDARD order, List<OUTP_ORDERS_STANDARD> addOrders,ref string strMsg)
        {
            // 检查并处理 performedBy 为空的情况，避免处方发药系统无法正常发药
            if (string.IsNullOrEmpty(result.Performed_dept))
            {
                // 如果当前订单有执行科室，使用当前订单的执行科室
                if (!string.IsNullOrEmpty(order.PERFORMED_BY))
                {
                    result.Performed_dept = order.PERFORMED_BY;
                }
                else
                {
                    strMsg = string.Format("药品【{0}】的执行科室为空，无法确定发药科室！请检查药品字典配置或手动选择药房。", result.ItemName);
                    return false;
                }
            }

            if (order.PERFORMED_BY != result.Performed_dept)
            {
                strMsg=string.Format("所选药品【{0}】药局与当前药品药局不一致，无法直接替换，\r\n请切到相同药局，或删除当前药品以后再重新录入！",result.ItemName);
                return false;
            }

            #region 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药
            DrugPriceListItem priceListItem = priceListDal.GetDrugPriceListItem(result);
            if (null == priceListItem)
            {
                strMsg = string.Format("未查询到药品【{0}】价格信息！",result.ItemName);
                return false;
            }
            string drugSpec = priceListItem.DRUG_SPEC ?? string.Empty;
            string firmId = priceListItem.FIRM_ID ?? string.Empty;
            string units = priceListItem.UNITS ?? string.Empty;
            decimal amountPerPackage = priceListItem.AMOUNT_PER_PACKAGE;
            string minSpec = priceListItem.MIN_SPEC ?? string.Empty;
            decimal price = 0;
            decimal chargePrice = 0;
            string armyResult = this.MilitaryMedicalReform(pi, result.ItemClass, result.ItemCode, result.ItemName, drugSpec);
            if (armyResult == "NoSel")
            {
                return false;
            }

            if (!priceListDal.GetClinicItemPrice(result, pi.CHARGE_TYPE, ref price, ref chargePrice))
            {
                strMsg="获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品的价格信息出错!";
                return false;
            }
            //套餐数量永远是1，调整
            //order.AMOUNT = result.LabAddFromType == Enums.AddFromType.Input ? 1 : result.ItemQuantity.ToDecimal(1);
            order.AMOUNT = result.ItemQuantity.ToDecimal(1);
            decimal amount = order.AMOUNT.ToDecimal(0);
            DRUG_DICT drugDict = DrugDictDal.GetDrugDictItem(result.ItemCode, minSpec);
            //单次用量，如果是套餐来的，则使用result的单次用量，输入法、常用项目和历史医嘱都使用drugDict的单次用量
            decimal dosage = result.LabAddFromType == Enums.AddFromType.Input ? drugDict.DOSE_PER_UNIT.ToDecimal(0) : result.Dose_per_unit.ToDecimal(0);
            string highDanger = CommFunc.StrEmpty(drugDict.HIGH_DANGER) ? string.Empty : drugDict.HIGH_DANGER == "1" ? "▲" : string.Empty;

            #endregion 提取药品信息（药品基础信息、价格、皮试、高危等），同时判断是否重复开药

            #region 药品库存判断
            decimal quantity = DrugDictDal.GetDrugInventoryInStorage(result.ItemCode, drugSpec, firmId, result.Performed_dept);
            if (quantity == -999)
            {
                strMsg="获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】不可供!";
                return false;
            }

            if (quantity <= 0 && PrescParameter.CheckAmount == "1")
            {
                if (PrescParameter.AmountMustEnough == "1")
                {
                    strMsg = "获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!";
                    return false;
                }
                else
                {
                    strMsg = "获取【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品在药房【" + result.Performed_name + "】库存不足!";
                    return false;
                }
            }
            #endregion 药品库存判断

            #region 取药品的拆分属性与公费用药类别
            string splitFlag = DrugDictDal.GetDrugSplitProperty(result.ItemCode, result.Performed_dept);
            DataTable table = DrugDictDal.GetOfficialCatalogInfo(pi.CHARGE_TYPE, result.ItemCode, minSpec);
            string officeClass;
            string memo;
            if (table == null || table.Rows.Count == 0)
            {
                officeClass = string.Empty;
                memo = string.Empty;
            }
            else
            {
                officeClass = table.Rows[0]["CLASS_NAME"].ToString(string.Empty);
                memo = table.Rows[0]["MEMO"].ToString(string.Empty);
            }
            #endregion 取药品的拆分属性与公费用药类别

            #region 药品的毒理属性
            CommFunc.SetDtToNull(table);
            string drugToxiProperty = CommFunc.StrEmpty(drugDict.TOXI_PROPERTY) ? "普通药品" : drugDict.TOXI_PROPERTY;
            table = DrugDictDal.GetToxiPropertyInfo(drugToxiProperty);
            int drugIndicator = drugDict.DRUG_INDICATOR;
            string prescAttrName;
            if (table == null && table.Rows.Count == 0)
            {
                prescAttrName = PrescParameter.PrescAttr;
            }
            else
            {
                prescAttrName = table.Rows[0]["PRESC_ATTR_NAME"].ToString(PrescParameter.PrescAttr);
            }
            prescAttrName = SetPrescAttr(prescAttrName, order.ORDERED_BY);

            if (PrescParameter.AutoDetachDrug == "1" && drugIndicator == 3)
            {
                drugIndicator = 1;
            }

            int toxiPorperty = CommFunc.JudgePoisonHempProperties(";" + drugToxiProperty + ";");
            if (!this.AuditAuthorityForDrug(drugDict))
            {
                return false;
            }
            if (PrescParameter.AutoDetachDrug == "0")//不自动分方的情况，只能添加毒理属性相同的药品
            {
                List<OUTP_ORDERS_STANDARD> list = addOrders.Where(r => r.ORDER_CLASS == "A").OrderBy(r => r.ORDER_NO).ThenBy(r => r.ORDER_SUB_NO).ToList();

                if (list.Count > 1)
                {
                    int find = list.FindIndex(r => r == order);
                    if (find == 0)
                    {
                        find++;
                    }
                    else
                    {
                        find--;
                    }
                    int nextToxiProperty = CommFunc.JudgePoisonHempProperties(";" + (CommFunc.StrEmpty(drugDict.TOXI_PROPERTY) ? "普通药品" : drugDict.TOXI_PROPERTY) + ";");
                    if (toxiPorperty != nextToxiProperty)
                    {
                        if (XtraMessageBox.Show("录入药品与已有药品毒理不同，是否在同一处方中录入?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) == DialogResult.No)
                        {
                            return false;
                        }
                    }
                }
            }
            #endregion 药品的毒理属性



            #region 设置药品的记录信息
            order.ORDER_TEXT = result.ItemName;
            order.ORDER_CODE = result.ItemCode;
            order.DOSAGE_UNITS = drugDict.DOSE_UNITS;
            order.ORDER_CLASS = result.ItemClass;
            order.ITEM_SPEC = drugSpec;
            order.FIRM_ID = firmId;
            order.UNITS = units;
            order.SPLIT_FLAG = Convert.ToInt32(splitFlag);
            order.OFFICIAL_CATALOG = officeClass;
            order.TOXI_PROPERTY = drugToxiProperty;
            order.DRUG_INDICATOR = drugIndicator;
            order.DOSE_PER_UNIT = drugDict.DOSE_PER_UNIT;
            order.AMOUNT_PER_PACKAGE = amountPerPackage;
            order.MIN_SPEC = minSpec;
            order.PERFORMED_BY = result.Performed_dept;
            order.ADMINISTRATION = result.Administration;
            if (!string.IsNullOrEmpty(result.Frequency))
            {
                order.FREQUENCY = result.Frequency;
                DataRow drFrequency = CommonDict.DtPerformFreqDict.AsEnumerable().FirstOrDefault(dr => dr["FREQ_DESC"].ToString("").Equals(result.Frequency));
                if (null != drFrequency)
                {
                    order.FREQ_COUNTER = drFrequency["FREQ_COUNTER"].ToString();
                    order.FREQ_INTERVAL = drFrequency["FREQ_INTERVAL"].ToString();
                    order.FREQ_INTERVAL_UNIT = drFrequency["FREQ_INTERVAL_UNITS"].ToString("");
                }
            }
            order.USABLE_QUANTITY = quantity;
            //order.REPETITION = DBNull.Value;
            order.BATCH_NO = result.BatchNo;//个人感觉批次应该在发药的时候再处理，不应该在加药的时候就选择，按我的理解，药品应该是先进先出，不应该是人为的控制开药的批次
            order.RECIPETYPE = armyResult;//军队医改2022
            order.DOSAGE = dosage;
            order.PRESC_ATTR = prescAttrName;
            if (result.LabAddFromType == Enums.AddFromType.Orders)
            {
                order.PERFORM_TIMES = result.PerformTimes;
                order.ABIDANCE = result.Abidance;
            }
            order.PERFORM_TIMES = result.PerformTimes;
            order.ABIDANCE = result.Abidance;
            if (amount == 0)
            {
                amount = 1;
            }
            order.ITEM_PRICE = price;
            order.PRICE = Math.Round(amount * price, 4);
            order.CHARGE_PRICE = Math.Round(amount * price, 4);
            order.COSTS = Math.Round(amount * price, 4);
            order.CHARGES = Math.Round(amount * chargePrice, 4);
            order.HIGH_DANGER = highDanger;
            if (highDanger == "▲")
            {
                XtraMessageBox.Show("【编码：" + result.ItemCode + "】，【名称：" + result.ItemName + "】的药品是高危药品，请注意！", "提示");
            }

            #endregion 设置药品的记录信息

            #region 生成处方记录对应的费用明细
            using (IOrderCosts costs = new OrderCostsBusiness())
            {
                if (!costs.AddOutpCostsByOrder(pi, order, false, true))
                {
                    return false;
                }
            }
            #endregion 生成处方记录对应的费用明细
            return true;
        }

        #region 普通处方‘ 若是急诊科是就是急诊处方 儿科是儿科处方
        /// <summary>
        /// 修改儿童处方 属性 根据科室确定
        /// </summary>
        /// <param name="clinic_type"></param>
        /// <param name="charge_type"></param>
        /// <param name="ls_presc_attr_name"></param>
        /// <param name="age"></param>
        /// <param name="dept_code"></param>
        /// <returns></returns>
        public string SetPrescAttr(string ls_presc_attr_name, string dept_code)
        {
            if (ls_presc_attr_name == "普通处方")
            {
                List<string> prescDeptLi = PrescParameter.CHILD_PRESC_ATTR.Split(';').ToList();
                List<string> jzDeptLi = PrescParameter.JZ_PRESC_ATTR.Split(';').ToList();
                if (prescDeptLi.Contains(dept_code))
                {
                    ls_presc_attr_name = "儿童处方";
                }
                else if (jzDeptLi.Contains(dept_code))
                {
                    ls_presc_attr_name = "急诊处方";
                }
            }
            return ls_presc_attr_name;
        }
        #endregion

        /// <summary>
        /// 写入调试日志到文件
        /// </summary>
        /// <param name="message">日志消息</param>
        protected void WriteDebugLog(string message)
        {
            try
            {
                // 修改日志路径到正确的位置：TJHisPlatEXE\Client\LOG\exLOG
                string logDir = Path.Combine(Application.StartupPath, "LOG", "exLOG");

                // 确保目录存在
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string logFile = Path.Combine(logDir, $"PrescBusiness_Debug_{DateTime.Now:yyyyMMdd}.log");
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}{Environment.NewLine}";

                // 使用FileStream确保文件能够正确写入
                using (FileStream fs = new FileStream(logFile, FileMode.Append, FileAccess.Write, FileShare.Read))
                using (StreamWriter writer = new StreamWriter(fs, Encoding.UTF8))
                {
                    writer.Write(logEntry);
                    writer.Flush();
                }
            }
            catch (Exception ex)
            {
                // 如果日志写入失败，尝试写入到临时目录
                try
                {
                    string tempLogFile = Path.Combine(Path.GetTempPath(), $"PrescBusiness_Debug_{DateTime.Now:yyyyMMdd}.log");
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [ERROR: {ex.Message}] {message}{Environment.NewLine}";
                    File.AppendAllText(tempLogFile, logEntry, Encoding.UTF8);
                }
                catch
                {
                    // 完全忽略日志写入错误，避免影响主要功能
                }
            }
        }

        /// <summary>
        /// 测试日志功能是否正常工作
        /// </summary>
        public void TestLogFunction()
        {
            WriteDebugLog("=== 日志功能测试开始 ===");
            WriteDebugLog($"当前时间: {DateTime.Now}");
            WriteDebugLog($"应用程序启动路径: {Application.StartupPath}");
            WriteDebugLog($"预期日志目录: {Path.Combine(Application.StartupPath, "LOG", "exLOG")}");
            WriteDebugLog("=== 日志功能测试结束 ===");
        }
    }
}
