[2025-08-29 12:30:35] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-29 12:30:58] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:30:58] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:00] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=新方, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:31:01] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:01] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:01] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:01] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:02] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:31:02] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:02] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:02] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:02] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:31:02] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:02] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:02] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:23] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-29 12:31:27] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:29] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=新方, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:31:29] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8136, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:31:29] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:29] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:29] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:31:30] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:31:30] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8136, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:31:30] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8136, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:31:30] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8136, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:31:30] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:47:02] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8136, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:47:02] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:47:02] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:02] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:02] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:47:03] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:47:03] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:03] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:03] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:47:03] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:47:03] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:03] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:03] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:47:05] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8135, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:47:05] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:47:05] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:47:05] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:47:05] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:11] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-29 12:48:18] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:18] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:20] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=新方, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:48:20] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8137, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:21] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:21] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8137, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:21] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:22] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8137, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:48:22] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:22] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:22] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:22] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8137, REPETITION=1, DOSAGE=10g, CurrentPresc.DOSAGE=10g
[2025-08-29 12:48:22] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=10g, prescListOrder.DOSAGE=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:22] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=10g, 处方列表dosage=10g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:22] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:29] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-29 12:48:32] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=新方, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:48:35] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8138, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:48:35] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=6742, REPETITION=1, DOSAGE=g, CurrentPresc.DOSAGE=g
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=枸杞子, item.DOSAGE=10g, prescListOrder.DOSAGE=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [SetAmout入口] 药品=枸杞子, 进入时dosage=10g, 处方列表dosage=g, item.AMOUNT=10, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=枸杞子, item.DOSAGE=10g, item.AMOUNT=10
[2025-08-29 12:48:35] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8138, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:35] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:48:36] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8138, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:48:36] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:36] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:36] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:48:36] [DEBUG] [teRepetition_EditValueChanged] currentPrescCopy构造 - APPOINT_NO=8138, REPETITION=1, DOSAGE=5g, CurrentPresc.DOSAGE=5g
[2025-08-29 12:48:36] [DEBUG] [RepetitionChanged] 调用SetAmout前 - 药品=砂仁, item.DOSAGE=5g, prescListOrder.DOSAGE=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:36] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=5g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:48:36] [DEBUG] [RepetitionChanged] 调用SetAmout后 - 药品=砂仁, item.DOSAGE=5g, item.AMOUNT=5
[2025-08-29 12:48:55] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=1, prescListOrder.AMOUNT=
[2025-08-29 12:48:59] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
[2025-08-29 12:49:10] [DEBUG] [SetAmout入口] 药品=砂仁, 进入时dosage=5g, 处方列表dosage=g, item.AMOUNT=5, prescListOrder.AMOUNT=
