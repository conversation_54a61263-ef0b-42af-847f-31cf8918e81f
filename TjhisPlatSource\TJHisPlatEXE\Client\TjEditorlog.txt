
2024/10/16 10:38:50 未能找到文件“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_72.tmr”。
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   在 System.IO.File.InternalReadAllText(String path, Encoding encoding, Boolean checkHost)
   在 System.IO.File.ReadAllText(String path)
   在 TJTextEditor.FileCommon.ReadFile(String fileName)
2024/10/16 10:38:52 文件内容解析错误！不正确的数据。

   在 System.Security.Cryptography.CryptographicException.ThrowCryptographicException(Int32 hr)
   在 System.Security.Cryptography.Utils._DecryptData(SafeKeyHandle hKey, Byte[] data, Int32 ib, Int32 cb, Byte[]& outputBuffer, Int32 outputOffset, PaddingMode PaddingMode, Boolean fDone)
   在 System.Security.Cryptography.CryptoAPITransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   在 System.Security.Cryptography.CryptoStream.FlushFinalBlock()
   在 System.Security.Cryptography.CryptoStream.Dispose(Boolean disposing)
   在 System.IO.Stream.Close()
   在 System.IO.StreamReader.Dispose(Boolean disposing)
   在 System.IO.TextReader.Dispose()
   在 TJTextEditor.TJTextDocument.GetDesDenCryptString(String data)
   在 TJTextEditor.TJTextDocument.DecryptToXmlDocument(String content)
2024/10/16 11:12:53 未能找到文件“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_81.tmr”。
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   在 System.IO.File.InternalReadAllText(String path, Encoding encoding, Boolean checkHost)
   在 System.IO.File.ReadAllText(String path)
   在 TJTextEditor.FileCommon.ReadFile(String fileName)
2024/10/16 11:12:57 文件内容解析错误！不正确的数据。

   在 System.Security.Cryptography.CryptographicException.ThrowCryptographicException(Int32 hr)
   在 System.Security.Cryptography.Utils._DecryptData(SafeKeyHandle hKey, Byte[] data, Int32 ib, Int32 cb, Byte[]& outputBuffer, Int32 outputOffset, PaddingMode PaddingMode, Boolean fDone)
   在 System.Security.Cryptography.CryptoAPITransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   在 System.Security.Cryptography.CryptoStream.FlushFinalBlock()
   在 System.Security.Cryptography.CryptoStream.Dispose(Boolean disposing)
   在 System.IO.Stream.Close()
   在 System.IO.StreamReader.Dispose(Boolean disposing)
   在 System.IO.TextReader.Dispose()
   在 TJTextEditor.TJTextDocument.GetDesDenCryptString(String data)
   在 TJTextEditor.TJTextDocument.DecryptToXmlDocument(String content)
2024/10/16 11:14:56 文件打开错误,可能文件已损坏，详细信息请查看错误日志。
System.IO.FileNotFoundException: 未能找到文件“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_82.tmr”。
文件名:“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_82.tmr”
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   在 System.IO.File.InternalReadAllText(String path, Encoding encoding, Boolean checkHost)
   在 System.IO.File.ReadAllText(String path)
   在 TJTextEditor.EMREditorControl.FileOpen(String fileName)
2024/10/16 11:19:11 未能找到文件“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_82.tmr”。
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   在 System.IO.File.InternalReadAllText(String path, Encoding encoding, Boolean checkHost)
   在 System.IO.File.ReadAllText(String path)
   在 TJTextEditor.FileCommon.ReadFile(String fileName)
2024/10/16 11:19:13 文件内容解析错误！不正确的数据。

   在 System.Security.Cryptography.CryptographicException.ThrowCryptographicException(Int32 hr)
   在 System.Security.Cryptography.Utils._DecryptData(SafeKeyHandle hKey, Byte[] data, Int32 ib, Int32 cb, Byte[]& outputBuffer, Int32 outputOffset, PaddingMode PaddingMode, Boolean fDone)
   在 System.Security.Cryptography.CryptoAPITransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   在 System.Security.Cryptography.CryptoStream.FlushFinalBlock()
   在 System.Security.Cryptography.CryptoStream.Dispose(Boolean disposing)
   在 System.IO.Stream.Close()
   在 System.IO.StreamReader.Dispose(Boolean disposing)
   在 System.IO.TextReader.Dispose()
   在 TJTextEditor.TJTextDocument.GetDesDenCryptString(String data)
   在 TJTextEditor.TJTextDocument.DecryptToXmlDocument(String content)
2024/10/16 11:20:15 未能找到文件“E:\tj\荣军\DonNet\TjhisPlatSource\TJHisPlatEXE\Client\Temp\000153215_1_82.tmr”。
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   在 System.IO.File.InternalReadAllText(String path, Encoding encoding, Boolean checkHost)
   在 System.IO.File.ReadAllText(String path)
   在 TJTextEditor.FileCommon.ReadFile(String fileName)
2024/10/16 11:20:17 文件内容解析错误！不正确的数据。

   在 System.Security.Cryptography.CryptographicException.ThrowCryptographicException(Int32 hr)
   在 System.Security.Cryptography.Utils._DecryptData(SafeKeyHandle hKey, Byte[] data, Int32 ib, Int32 cb, Byte[]& outputBuffer, Int32 outputOffset, PaddingMode PaddingMode, Boolean fDone)
   在 System.Security.Cryptography.CryptoAPITransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   在 System.Security.Cryptography.CryptoStream.FlushFinalBlock()
   在 System.Security.Cryptography.CryptoStream.Dispose(Boolean disposing)
   在 System.IO.Stream.Close()
   在 System.IO.StreamReader.Dispose(Boolean disposing)
   在 System.IO.TextReader.Dispose()
   在 TJTextEditor.TJTextDocument.GetDesDenCryptString(String data)
   在 TJTextEditor.TJTextDocument.DecryptToXmlDocument(String content)
2024/10/17 12:05:30 未能加载文件或程序集“Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed”或它的某一个依赖项。找到的程序集清单定义与程序集引用不匹配。 (异常来自 HRESULT:0x80131040)
   在 Tjhis.Interface.CA.CaYXQ.CaYXQClass.cancelAuthByUserId(String userId)
   在 Tjhis.Interface.CA.CaYXQ.CaYXQ.LogOut(String userId) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\TjhisInterfaceCA\CaYXQ\CaYXQ.cs:行号 80
   在 Tjhis.Interface.CA.CaBusiness.CALogOut(String user) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\TjhisInterfaceCA\CaBusiness.cs:行号 454
   在 PlatCommonForm.FrmNewMain.FrmNewMain_FormClosing(Object sender, FormClosingEventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\PlatCommonForm\FrmNewMain.cs:行号 379
   在 System.Windows.Forms.Form.OnFormClosing(FormClosingEventArgs e)
   在 System.Windows.Forms.Form.WmClose(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/10/17 16:16:51 未能加载文件或程序集“Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed”或它的某一个依赖项。找到的程序集清单定义与程序集引用不匹配。 (异常来自 HRESULT:0x80131040)
   在 Tjhis.Interface.CA.CaYXQ.CaYXQClass.cancelAuthByUserId(String userId)
   在 Tjhis.Interface.CA.CaYXQ.CaYXQ.LogOut(String userId) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\TjhisInterfaceCA\CaYXQ\CaYXQ.cs:行号 80
   在 Tjhis.Interface.CA.CaBusiness.CALogOut(String user) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\TjhisInterfaceCA\CaBusiness.cs:行号 454
   在 PlatCommonForm.FrmNewMain.FrmNewMain_FormClosing(Object sender, FormClosingEventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\PlatCommonForm\FrmNewMain.cs:行号 379
   在 System.Windows.Forms.Form.OnFormClosing(FormClosingEventArgs e)
   在 System.Windows.Forms.Form.WmClose(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:26:47 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:26:49 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:26:52 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:26:52 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:26:53 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:37:22 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:38 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:40 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:40 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:41 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:55 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 System.Collections.Hashtable.Insert(Object key, Object nvalue, Boolean add)
   在 System.Collections.Hashtable.Add(Object key, Object value)
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateAllNodes()
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateNodes(TreeListNode parent)
   在 DevExpress.XtraTreeList.TreeList.LoadNodes()
   在 DevExpress.XtraTreeList.TreeList.DoDataSourceChanged()
   在 DevExpress.XtraTreeList.TreeList.UpdateDataSource(Boolean updateContent)
   在 DevExpress.XtraTreeList.TreeList.OnLoadedCore()
   在 DevExpress.XtraTreeList.TreeList.OnLoaded()
   在 DevExpress.XtraEditors.Container.EditorContainer.OnVisibleChanged(EventArgs e)
   在 DevExpress.XtraTreeList.TreeList.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Form.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:56 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 System.Collections.Hashtable.Insert(Object key, Object nvalue, Boolean add)
   在 System.Collections.Hashtable.Add(Object key, Object value)
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateAllNodes()
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateNodes(TreeListNode parent)
   在 DevExpress.XtraTreeList.TreeList.LoadNodes()
   在 DevExpress.XtraTreeList.TreeList.DoDataSourceChanged()
   在 DevExpress.XtraTreeList.TreeList.UpdateDataSource(Boolean updateContent)
   在 DevExpress.XtraTreeList.TreeList.OnLoadedCore()
   在 DevExpress.XtraTreeList.TreeList.OnLoaded()
   在 DevExpress.XtraEditors.Container.EditorContainer.OnVisibleChanged(EventArgs e)
   在 DevExpress.XtraTreeList.TreeList.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Form.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:57 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 System.Collections.Hashtable.Insert(Object key, Object nvalue, Boolean add)
   在 System.Collections.Hashtable.Add(Object key, Object value)
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateAllNodes()
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateNodes(TreeListNode parent)
   在 DevExpress.XtraTreeList.TreeList.LoadNodes()
   在 DevExpress.XtraTreeList.TreeList.DoDataSourceChanged()
   在 DevExpress.XtraTreeList.TreeList.UpdateDataSource(Boolean updateContent)
   在 DevExpress.XtraTreeList.TreeList.OnLoadedCore()
   在 DevExpress.XtraTreeList.TreeList.OnLoaded()
   在 DevExpress.XtraEditors.Container.EditorContainer.OnVisibleChanged(EventArgs e)
   在 DevExpress.XtraTreeList.TreeList.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Form.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:58 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 System.Collections.Hashtable.Insert(Object key, Object nvalue, Boolean add)
   在 System.Collections.Hashtable.Add(Object key, Object value)
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateAllNodes()
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateNodes(TreeListNode parent)
   在 DevExpress.XtraTreeList.TreeList.LoadNodes()
   在 DevExpress.XtraTreeList.TreeList.DoDataSourceChanged()
   在 DevExpress.XtraTreeList.TreeList.UpdateDataSource(Boolean updateContent)
   在 DevExpress.XtraTreeList.TreeList.OnLoadedCore()
   在 DevExpress.XtraTreeList.TreeList.OnLoaded()
   在 DevExpress.XtraEditors.Container.EditorContainer.OnVisibleChanged(EventArgs e)
   在 DevExpress.XtraTreeList.TreeList.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Form.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:39:58 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 System.Collections.Hashtable.Insert(Object key, Object nvalue, Boolean add)
   在 System.Collections.Hashtable.Add(Object key, Object value)
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateAllNodes()
   在 DevExpress.XtraTreeList.Data.TreeListBoundData.CreateNodes(TreeListNode parent)
   在 DevExpress.XtraTreeList.TreeList.LoadNodes()
   在 DevExpress.XtraTreeList.TreeList.DoDataSourceChanged()
   在 DevExpress.XtraTreeList.TreeList.UpdateDataSource(Boolean updateContent)
   在 DevExpress.XtraTreeList.TreeList.OnLoadedCore()
   在 DevExpress.XtraTreeList.TreeList.OnLoaded()
   在 DevExpress.XtraEditors.Container.EditorContainer.OnVisibleChanged(EventArgs e)
   在 DevExpress.XtraTreeList.TreeList.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnParentVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.ScrollableControl.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Form.OnVisibleChanged(EventArgs e)
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:46:04 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:46:08 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:46:09 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:46:10 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/1 15:46:10 已添加项。字典中的关键字:“T_3326165”所添加的关键字:“T_3326165”
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2024/11/4 11:49:40 打开数据库连接失败!
   在 Utility.OracleODP.OracleBaseClass.OpenDB() 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\DataAccess\Utility\OracleODP\OracleBaseClass.cs:行号 209
   在 OracleDAL.ServerPublic_Dao.GetDataBySql(String sql) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\DataAccess\OracleDAL\ServerPublic_Dao.cs:行号 131
   在 NM_Service.NMService.ServerPublicClient.GetList(String strSql) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\DataAccess\NMService\ServerPublic_Imp.cs:行号 181
   在 PlatCommon.Common.PublicFunction.GetSystemAuthorization(String hisunitcode, String as_app_code, String as_app_name, String as_app_version, String encryptCode, String& msg, Int32& as_day) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\PlatCommon\Common\PublicFunction.cs:行号 1798
   在 PlatCommonForm.FrmNewMain.timer1_Tick(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\PlatCommonForm\FrmNewMain.cs:行号 1749
   在 System.Windows.Forms.Timer.OnTick(EventArgs e)
   在 System.Windows.Forms.Timer.TimerNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 System.Windows.Forms.UnsafeNativeMethods.DispatchMessageW(MSG& msg)
   在 System.Windows.Forms.Application.ComponentManager.System.Windows.Forms.UnsafeNativeMethods.IMsoComponentManager.FPushMessageLoop(IntPtr dwComponentID, Int32 reason, Int32 pvLoopData)
   在 System.Windows.Forms.Application.ThreadContext.RunMessageLoopInner(Int32 reason, ApplicationContext context)
   在 System.Windows.Forms.Application.ThreadContext.RunMessageLoop(Int32 reason, ApplicationContext context)
   在 System.Windows.Forms.Application.RunDialog(Form form)
   在 System.Windows.Forms.Form.ShowDialog(IWin32Window owner)
   在 DevExpress.XtraEditors.XtraBaseForm.DoShowDialog(IWin32Window owner)
   在 DevExpress.XtraEditors.XtraBaseForm.ShowMessageBoxDialog()
   在 DevExpress.XtraEditors.XtraMessageBoxForm.ShowMessageBoxDialog(XtraMessageBoxArgs message)
   在 DevExpress.XtraEditors.XtraMessageBox.Show(XtraMessageBoxArgs args)
   在 DevExpress.XtraEditors.XtraMessageBox.Show(String text)
   在 TJHisPlat.Program.Main(String[] args) 位置 E:\tj\荣军\DonNet\TjhisPlatFrame\TJHisPlat\Program.cs:行号 234
2024/11/4 14:52:49 未找到列 [INPUT_CODE]。
   在 System.Data.NameNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.DataExpression.Bind(DataTable table)
   在 System.Data.DataExpression..ctor(DataTable table, String expression, Type type)
   在 System.Data.DataView.set_RowFilter(String value)
   在 Tjhis.Outpdoct.Station.Diagnosis.Views.FrmCdiagItem.btnSearch_Click(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Diagnosis\Views\FrmCdiagItem.cs:行号 56
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChangedCore(EventArgs e)
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChanged(EventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.RaiseEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.set_EditValue(Object value)
   在 DevExpress.XtraEditors.TextEdit.OnMaskBox_ValueChanged(Object sender, EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.RaiseEditTextChanged(EventArgs args)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoAfterTextChanged(EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.OnTextChanged(EventArgs e)
   在 System.Windows.Forms.TextBoxBase.WmReflectCommand(Message& m)
   在 System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   在 System.Windows.Forms.TextBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.BaseWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.TextBoxMaskBox.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2024/11/4 14:52:53 未找到列 [INPUT_CODE]。
   在 System.Data.NameNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.DataExpression.Bind(DataTable table)
   在 System.Data.DataExpression..ctor(DataTable table, String expression, Type type)
   在 System.Data.DataView.set_RowFilter(String value)
   在 Tjhis.Outpdoct.Station.Diagnosis.Views.FrmCdiagItem.btnSearch_Click(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Diagnosis\Views\FrmCdiagItem.cs:行号 56
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChangedCore(EventArgs e)
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChanged(EventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.RaiseEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.set_EditValue(Object value)
   在 DevExpress.XtraEditors.TextEdit.OnMaskBox_ValueChanged(Object sender, EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.RaiseEditTextChanged(EventArgs args)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoAfterTextChanged(EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.OnTextChanged(EventArgs e)
   在 System.Windows.Forms.TextBoxBase.WmReflectCommand(Message& m)
   在 System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   在 System.Windows.Forms.TextBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.BaseWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.TextBoxMaskBox.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2024/11/4 14:52:54 未找到列 [INPUT_CODE]。
   在 System.Data.NameNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.DataExpression.Bind(DataTable table)
   在 System.Data.DataExpression..ctor(DataTable table, String expression, Type type)
   在 System.Data.DataView.set_RowFilter(String value)
   在 Tjhis.Outpdoct.Station.Diagnosis.Views.FrmCdiagItem.btnSearch_Click(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Diagnosis\Views\FrmCdiagItem.cs:行号 56
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChangedCore(EventArgs e)
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChanged(EventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.RaiseEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.set_EditValue(Object value)
   在 DevExpress.XtraEditors.TextEdit.OnMaskBox_ValueChanged(Object sender, EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.RaiseEditTextChanged(EventArgs args)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoAfterTextChanged(EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.OnTextChanged(EventArgs e)
   在 System.Windows.Forms.TextBoxBase.WmReflectCommand(Message& m)
   在 System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   在 System.Windows.Forms.TextBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.BaseWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.TextBoxMaskBox.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2024/11/4 14:52:55 未找到列 [INPUT_CODE]。
   在 System.Data.NameNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.DataExpression.Bind(DataTable table)
   在 System.Data.DataExpression..ctor(DataTable table, String expression, Type type)
   在 System.Data.DataView.set_RowFilter(String value)
   在 Tjhis.Outpdoct.Station.Diagnosis.Views.FrmCdiagItem.btnSearch_Click(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Diagnosis\Views\FrmCdiagItem.cs:行号 56
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChangedCore(EventArgs e)
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChanged(EventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.RaiseEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.set_EditValue(Object value)
   在 DevExpress.XtraEditors.TextEdit.OnMaskBox_ValueChanged(Object sender, EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.RaiseEditTextChanged(EventArgs args)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoAfterTextChanged(EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.OnTextChanged(EventArgs e)
   在 System.Windows.Forms.TextBoxBase.WmReflectCommand(Message& m)
   在 System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   在 System.Windows.Forms.TextBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.BaseWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.TextBoxMaskBox.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2024/11/4 14:52:56 未找到列 [INPUT_CODE]。
   在 System.Data.NameNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.BinaryNode.Bind(DataTable table, List`1 list)
   在 System.Data.DataExpression.Bind(DataTable table)
   在 System.Data.DataExpression..ctor(DataTable table, String expression, Type type)
   在 System.Data.DataView.set_RowFilter(String value)
   在 Tjhis.Outpdoct.Station.Diagnosis.Views.FrmCdiagItem.btnSearch_Click(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Diagnosis\Views\FrmCdiagItem.cs:行号 56
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChangedCore(EventArgs e)
   在 DevExpress.XtraEditors.Repository.RepositoryItem.RaiseEditValueChanged(EventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.RaiseEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanged()
   在 DevExpress.XtraEditors.BaseEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.TextEdit.OnEditValueChanging(ChangingEventArgs e)
   在 DevExpress.XtraEditors.BaseEdit.set_EditValue(Object value)
   在 DevExpress.XtraEditors.TextEdit.OnMaskBox_ValueChanged(Object sender, EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.RaiseEditTextChanged(EventArgs args)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoAfterTextChanged(EventArgs e)
   在 DevExpress.XtraEditors.Mask.MaskBox.OnTextChanged(EventArgs e)
   在 System.Windows.Forms.TextBoxBase.WmReflectCommand(Message& m)
   在 System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   在 System.Windows.Forms.TextBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.BaseWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.MaskStrategy.SimpleStrategy.DoWndProc(Message& m)
   在 DevExpress.XtraEditors.Mask.MaskBox.WndProc(Message& m)
   在 DevExpress.XtraEditors.TextBoxMaskBox.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/6 9:45:48 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:51 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:52 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:52 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:53 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:53 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:54 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/6 9:45:54 未将对象引用设置到对象的实例。
   在 Tjhis.Outpdoct.Station.Views.Print.FrmOutpPrint.FrmOutpPrint_Load(Object sender, EventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\Print\FrmOutpPrint.cs:行号 483
   在 System.EventHandler.Invoke(Object sender, EventArgs e)
   在 System.Windows.Forms.Form.OnLoad(EventArgs e)
   在 DevExpress.XtraEditors.XtraForm.OnLoad(EventArgs e)
   在 System.Windows.Forms.Form.OnCreateControl()
   在 System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   在 System.Windows.Forms.Control.CreateControl()
   在 System.Windows.Forms.Control.WmShowWindow(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 System.Windows.Forms.Form.WmShowWindow(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/1/21 11:00:26 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.ContainerControl.FocusActiveControlInternal()
   在 System.Windows.Forms.ContainerControl.WmSetFocus(Message& m)
   在 System.Windows.Forms.ContainerControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ZIndexControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.DockPanel.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/21 11:05:27 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.ContainerControl.FocusActiveControlInternal()
   在 System.Windows.Forms.ContainerControl.WmSetFocus(Message& m)
   在 System.Windows.Forms.ContainerControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ZIndexControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.DockPanel.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/21 11:29:44 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.Control.get_HostedInWin32DialogManager()
   在 System.Windows.Forms.Control.get_HostedInWin32DialogManager()
   在 System.Windows.Forms.Control.WmSetFocus(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ControlContainer.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/21 11:29:53 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.Control.get_HostedInWin32DialogManager()
   在 System.Windows.Forms.Control.get_HostedInWin32DialogManager()
   在 System.Windows.Forms.Control.WmSetFocus(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 System.Windows.Forms.ScrollableControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ControlContainer.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/21 11:42:52 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.ContainerControl.FocusActiveControlInternal()
   在 System.Windows.Forms.ContainerControl.WmSetFocus(Message& m)
   在 System.Windows.Forms.ContainerControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ZIndexControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.DockPanel.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/1/21 14:11:42 创建窗口句柄时出错。
   在 System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 DevExpress.XtraEditors.XtraForm.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 System.Windows.Forms.ContainerControl.FocusActiveControlInternal()
   在 System.Windows.Forms.ContainerControl.WmSetFocus(Message& m)
   在 System.Windows.Forms.ContainerControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.ZIndexControl.WndProc(Message& m)
   在 DevExpress.XtraBars.Docking.DockPanel.WndProc(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/6/16 14:52:12 该字符串未被识别为有效的布尔值。
   在 System.Boolean.Parse(String value)
   在 System.Convert.ToBoolean(String value)
   在 Tjhis.Outpdoct.Station.Common.OtherParameter.get_IsSave662Data() 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Common\OutpdoctParameter.cs:行号 894
   在 Tjhis.Outpdoct.Station.Business.DataVersionFactory.GetOrderVersion() 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Business\DataVersionFactory.cs:行号 19
   在 Tjhis.Outpdoct.Station.Business.ExamApply..ctor(IOrders OrderBusiness) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Business\ExamApply.cs:行号 43
   在 Tjhis.Outpdoct.Station.Business.OrderBusiness..ctor() 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Business\OrderBusiness.cs:行号 40
   在 Tjhis.Outpdoct.Station.Views.FrmOutpPatientList.FormOutpPatientList_FormClosing(Object sender, FormClosingEventArgs e) 位置 E:\tj\荣军\DonNet\TjhisPlatSource\Tjhis_Outpdoct_station\Views\FrmOutpPatientList.cs:行号 1800
   在 System.Windows.Forms.Form.OnFormClosing(FormClosingEventArgs e)
   在 System.Windows.Forms.Form.WmClose(Message& m)
   在 System.Windows.Forms.Form.WndProc(Message& m)
   在 DevExpress.XtraEditors.XtraForm.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
   在 DevExpress.Utils.Drawing.Helpers.NativeMethods.UnsafeNativeMethods.DefSubclassProc(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam)
   在 DevExpress.Utils.Drawing.Helpers.Win32SubclasserFactory.Win32Subclasser.SubClassProcInner(IntPtr hWnd, IntPtr Msg, IntPtr wParam, IntPtr lParam, IntPtr uIdSubclass, IntPtr dwRefData)
2025/7/20 19:48:25 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/7/20 19:48:29 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/7/20 19:48:29 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/8/29 11:41:26 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/8/29 11:41:33 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/8/29 11:41:33 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/8/29 11:41:34 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
2025/8/29 11:41:34 尝试读取或写入受保护的内存。这通常指示其他内存已损坏。
   在 System.Runtime.InteropServices.Marshal.ReadInt32(IntPtr ptr, Int32 ofs)
   在 DevExpress.Skins.SkinImageColorizer.CalcLightness(Image sourceImage)
   在 DevExpress.Skins.SkinImageColorizer.GetSmartColorizedImage(Image image, Color color)
   在 DevExpress.Skins.SkinImage.GetColoredImages(Image image)
   在 DevExpress.Skins.SkinImage.GetColoredImage(Image image)
   在 DevExpress.Skins.SkinImage.get_Image()
   在 DevExpress.Skins.SkinImage.GetImageBounds(Int32 index)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage, SkinPaddingEdges padding)
   在 DevExpress.Skins.SkinElementPainter.DrawSkinImage(SkinElementInfo elementInfo, SkinImage skinImage)
   在 DevExpress.Skins.SkinElementPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.ObjectPainter.DrawObject(GraphicsCache cache, ObjectPainter painter, ObjectInfoArgs e)
   在 DevExpress.Utils.Drawing.RotateObjectPaintHelper.DrawRotated(GraphicsCache cache, ObjectInfoArgs info, ObjectPainter painter, RotateFlipType rotate, Boolean alwaysCreate)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawButton(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.EditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.SkinEditorButtonPainter.DrawObject(ObjectInfoArgs e)
   在 DevExpress.XtraEditors.Drawing.BaseButtonPainter.DrawContent(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.Drawing.BaseControlPainter.Draw(ControlGraphicsInfoArgs info)
   在 DevExpress.XtraEditors.BaseControl.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.BaseButton.OnPaint(PaintEventArgs e)
   在 DevExpress.XtraEditors.SimpleButton.OnPaint(PaintEventArgs e)
   在 System.Windows.Forms.Control.PaintWithErrorHandling(PaintEventArgs e, Int16 layer)
   在 System.Windows.Forms.Control.WmPaint(Message& m)
   在 System.Windows.Forms.Control.WndProc(Message& m)
   在 DevExpress.Utils.Controls.ControlBase.WndProc(Message& m)
   在 DevExpress.XtraEditors.BaseControl.WndProc(Message& msg)
   在 System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message& m)
   在 System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   在 System.Windows.Forms.NativeWindow.DebuggableCallback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)