﻿using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Tjhis.Outpdoct.Station.Common
{
    /// <summary>
    /// 药品相关参数
    /// </summary>
    public class PrescParameter
    {
        private static int _maxPrescCount = -1;
        /// <summary>
        /// 最大西药处方数量 liujun add 2023-2-24
        /// </summary>
        public static int MaxDrugCount
        {
            get
            {
                string value = SystemParm.GetParameterValue("MAXPRESC", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _maxPrescCount = value.ToInt(5);
                return _maxPrescCount;
            }
            private set
            {
                _maxPrescCount = value;
            }
        }

        private static int _maxCPrescCount = -1;
        /// <summary>
        /// 最大草药处方数量 liujun add 2023-3-13
        /// </summary>
        public static int MaxCDrugCount
        {
            get
            {
                string value = SystemParm.GetParameterValue("CDRUGMAXPRESC", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _maxCPrescCount = value.ToInt(20);
                return _maxCPrescCount;
            }
            private set
            {
                _maxCPrescCount = value;
            }
        }


        private static string _checkAmount = "-1";
        /// <summary>
        /// 是否校验药品库存，1==校验(默认)，0==不校验 liujun add 2023-2-27
        /// </summary>
        public static string CheckAmount
        {
            get
            {
                string value = SystemParm.GetParameterValue("CHECK_AMOUNT", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _checkAmount = value.ToString("1");
                return _checkAmount;
            }
            private set
            {
                _checkAmount = value;
            }
        }

        private static string _amountMustEnough = "-1";
        /// <summary>
        /// 选择药品时，校验药品库存是否必须充足，0==不必(默认)，1==必须充足 liujun add 2023-2-27
        /// </summary>
        public static string AmountMustEnough
        {
            get
            {
                string value = SystemParm.GetParameterValue("AMOUNT_MUST_ENOUGH", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _amountMustEnough = value.ToString("0");
                return _amountMustEnough;
            }
            private set
            {
                _amountMustEnough = value;
            }
        }

        private static string _autoDetachDrug = "-1";
        /// <summary>
        /// 自动分方标志  liujun add 2023-2-27
        /// </summary>
        public static string AutoDetachDrug
        {
            get
            {
                string value = SystemParm.GetParameterValue("AUTO_DETACH_DRUG", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _autoDetachDrug = value.ToString("1");
                return _autoDetachDrug;
            }
            private set
            {
                _autoDetachDrug = value;
            }
        }

        private static string _prescToxiProtitiesDuma = "-1";
        /// <summary>
        /// 毒麻属性 liujun add 2023-2-27
        /// </summary>
        public static string PrescToxiProtitiesDuma
        {
            get
            {
                string value = SystemParm.GetParameterValue("PRESC_TOXI_PROTITIES_DUMA", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _prescToxiProtitiesDuma = ";" + value.ToString("") + ";";
                return _prescToxiProtitiesDuma;
            }
            private set
            {
                _prescToxiProtitiesDuma = value;
            }
        }

        private static string _prescToxiProtitiesJingshen = "-1";
        /// <summary>
        /// 精神二类属性 liujun add 2023-2-27
        /// </summary>
        public static string PrescToxiProtitiesJingshen
        {
            get
            {
                string value = SystemParm.GetParameterValue("PRESC_TOXI_PROTITIES_JINGSHEN", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _prescToxiProtitiesJingshen = ";" + value.ToString("") + ";";
                return _prescToxiProtitiesJingshen;
            }
            private set
            {
                _prescToxiProtitiesJingshen = value;
            }
        }

        private static string _checkDmClass = "-1";
        /// <summary>
        /// 限制开药 liujun add 2023-2-27
        /// </summary>
        public static string CheckDmClass
        {
            get
            {
                string value = SystemParm.GetParameterValue("CHECK_DM_CLASS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _checkDmClass = value.ToString("0");
                return _checkDmClass;
            }
            private set
            {
                _checkDmClass = value;
            }
        }

        private static string _skinAdministration = "-1";
        /// <summary>
        /// 皮试途径 liujun add 2023-2-27
        /// </summary>
        public static string SkinAdministration
        {
            get
            {
                string value = SystemParm.GetParameterValue("SKIN_ADMINISTRATION", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _skinAdministration = value.ToString("皮内注射");
                return _skinAdministration;
            }
            private set
            {
                _skinAdministration = value;
            }
        }

        private static string _armyzjbx = "-1";
        /// <summary>
        /// 乙类限制 liujun add 2023-2-28
        /// </summary>
        public static string ArmyZjbx
        {
            get
            {
                string value = SystemParm.GetParameterValue("ARMYZJBX", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _armyzjbx = value.ToString("皮内注射");
                return _armyzjbx;
            }
            private set
            {
                _armyzjbx = value;
            }
        }

        private static string _managerBatchNo = "-1";
        /// <summary>
        /// 管理批次号 liujun add 2023-3-3
        /// </summary>
        public static string ManagerBatchNo
        {
            get
            {
                string value = SystemParm.GetParameterValue("MANAGERBATCHNO", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _managerBatchNo = value.ToString("0");
                return _managerBatchNo;
            }
            private set
            {
                _managerBatchNo = value;
            }
        }

        private static string _reformBillingFlagRecorded = "-1";

        public static string ReformBillingFlagRecorded
        {
            get
            {
                string value = SystemParm.GetParameterValue("REFORM_BILLING_FLAG_RECORDED", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _reformBillingFlagRecorded = value.ToString("0");
                return _reformBillingFlagRecorded;
            }
            private set
            {
                _reformBillingFlagRecorded = value;
            }
        }
        /// <summary>
        /// Costs字段精度 liujun add 2023-3-6
        /// </summary>
        public static int CostsPrecision
        {
            get
            {
                return 2;
            }
        }
        /// <summary>
        /// Charges字段精度 liujun add 2023-3-6
        /// </summary>
        public static int ChargesPrecision
        {
            get
            {
                return 2;
            }
        }

        private static string _autoAdministration = "-1";
        /// <summary>
        /// 药品绑定途径 liujun add 2023-3-6
        /// </summary>
        public static string AutoAdministration
        {
            get
            {
                string value = SystemParm.GetParameterValue("AUTO_ADMINISTRATION", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _autoAdministration = value.ToString("0");
                return _autoAdministration;
            }
            private set
            {
                _autoAdministration = value;
            }
        }

        private static string _maxPrescDays = "-1";
        /// <summary>
        /// 最大用药天数 liujun add 2023-3-10
        /// </summary>
        public static string MaxPrescDays
        {
            get
            {
                string value = SystemParm.GetParameterValue("MAX_PRESC_DAYS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _maxPrescDays = value.ToString("0");
                return _maxPrescDays;
            }
            private set
            {
                _maxPrescDays = value;
            }
        }

        private static string _checkModel = "-1";
        /// <summary>
        /// 库存校验方法，0：不校验待发药与开单未缴费；1校验待发药但不校验开单未缴费；2校验待发药与开单未缴费 liujun add 2023-3-10
        /// </summary>
        public static string CheckModel
        {
            get
            {
                string value = SystemParm.GetParameterValue("CHECK_MODE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _checkModel = value.ToString("0");
                return _checkModel;
            }
            private set
            {
                _checkModel = value;
            }
        }
        private static string infection_drug_storage;
        /// <summary>
        /// 特殊药局
        /// </summary>
        public static string INFECTION_DRUG_STORAGE
        {
            get
            {
                infection_drug_storage = SystemParm.GetParameterValue("INFECTION_DRUG_STORAGE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return infection_drug_storage;
            }
            private set
            {
                infection_drug_storage = value;
            }
        }
        private static string presc_drug_disps;
        /// <summary>
        /// 西药发药药房
        /// </summary>
        public static string PRESC_DRUG_DISPS
        {
            get
            {
                presc_drug_disps = SystemParm.GetParameterValue("PRESC_DRUG_DISPS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return presc_drug_disps;
            }
            private set
            {
                presc_drug_disps = value;
            }
        }

        private static string presc_cdrug_disps;
        /// <summary>
        /// 中药发药药房
        /// </summary>
        public static string PRESC_CDRUG_DISPS
        {
            get
            {
                presc_cdrug_disps = SystemParm.GetParameterValue("PRESC_CDRUG_DISPS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return presc_cdrug_disps;
            }
            private set
            {
                presc_cdrug_disps = value;
            }
        }
        private static string _CDRUG_FREQ_DETAIL;
        private static string _passFirm;

        /// <summary>
        /// 煎法
        /// </summary>
        public static string CDRUG_FREQ_DETAIL
        {
            get
            {
                _CDRUG_FREQ_DETAIL = SystemParm.GetParameterValue("CDRUG_FREQ_DETAIL", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _CDRUG_FREQ_DETAIL;
            }
            private set
            {
                _CDRUG_FREQ_DETAIL = value;
            }
        }

        private static string _CDRUG_SPECIAL_REQUEST;

        /// <summary>
        /// 草药特殊要求
        /// </summary>
        public static string CDRUG_SPECIAL_REQUEST
        {
            get
            {
                _CDRUG_SPECIAL_REQUEST = SystemParm.GetParameterValue("CDRUG_SPECIAL_REQUEST", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _CDRUG_SPECIAL_REQUEST;
            }
            private set
            {
                _CDRUG_SPECIAL_REQUEST = value;
            }
        }

        private static string _ABIDANCE_MEMO_INP = "NO";

        /// <summary>
        /// 超过七天药品，是否录入用药原因（YES-需要录入用药原因，NO-不需要录入用药原因）
        /// </summary>
        public static string ABIDANCE_MEMO_INP
        {
            get
            {
                _ABIDANCE_MEMO_INP = SystemParm.GetParameterValue("ABIDANCE_MEMO_INP", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _ABIDANCE_MEMO_INP;
            }
            private set
            {
                _ABIDANCE_MEMO_INP = value;
            }
        }

        private static string _AUTO_CHANGE_STATUS = "0";

        /// <summary>
        /// 保存开单时是否自动将当前病人设置为已接诊（1-自动设置，0-手动）
        /// </summary>
        public static string AUTO_CHANGE_STATUS
        {
            get
            {
                _AUTO_CHANGE_STATUS = SystemParm.GetParameterValue("AUTO_CHANGE_STATUS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _AUTO_CHANGE_STATUS;
            }
            private set
            {
                _AUTO_CHANGE_STATUS = value;
            }
        }

        private static string prescAttr;
        /// <summary>
        /// 处方属性
        /// </summary>
        public static string PrescAttr
        {
            get
            {
                prescAttr = "普通处方";
                return prescAttr;
            }
        }
        /// <summary>
        /// 合理用药厂家
        /// </summary>
        public static string PassFirm
        {
            get
            {
                _passFirm = SystemParm.GetParameterValue("PASS_FIRM", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _passFirm;
            }
            private set => _passFirm = value;
        }

        private static string _MAX_CHARGES = "0";
        /// <summary>
        /// 单张处方最大费用金额（0-不校验）
        /// </summary>
        public static string MAX_CHARGES
        {
            get
            {
                _MAX_CHARGES = SystemParm.GetParameterValue("MAX_CHARGES", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _MAX_CHARGES;
            }
            private set
            {
                _MAX_CHARGES = value;
            }
        }

        private static string jz_PrescAttr;
        /// <summary>
        /// 急诊处方科室设定
        /// </summary>
        public static string JZ_PRESC_ATTR
        {
            get
            {
                jz_PrescAttr = SystemParm.GetParameterValue("JZ_PRESC_DEPT", "OUTPDOCT", "*", "*", SystemParm.HisUnitCode);
                return jz_PrescAttr;
            }
            private set
            {
                jz_PrescAttr = value;
            }
        }
        private static string child_PrescAttr;
        /// <summary>
        /// 儿科处方科室设定
        /// </summary>
        public static string CHILD_PRESC_ATTR
        {
            get
            {
                child_PrescAttr = SystemParm.GetParameterValue("CHILD_PRESC_DEPT", "OUTPDOCT", "*", "*", SystemParm.HisUnitCode);
                return child_PrescAttr;
            }
            private set
            {
                child_PrescAttr = value;
            }
        }
        public static void Clear()
        {
            CheckModel = "-1";
            MaxPrescDays = "-1";
            AutoAdministration = "-1";
            ReformBillingFlagRecorded = "-1";
            ManagerBatchNo = "-1";
            ArmyZjbx = "-1";
            SkinAdministration = "-1";
            CheckDmClass = "-1";
            PrescToxiProtitiesJingshen = "-1";
            PrescToxiProtitiesDuma = "-1";
            AutoDetachDrug = "-1";
            AmountMustEnough = "-1";
            CheckAmount = "-1";
            MaxCDrugCount = -1;
            MaxDrugCount = -1;
            INFECTION_DRUG_STORAGE = "";
            PRESC_DRUG_DISPS = "";
            PRESC_CDRUG_DISPS = "";
            CDRUG_FREQ_DETAIL = "";
        }

    }


    /// <summary>
    /// 申请类医嘱相关参数
    /// </summary>
    public class ApplyParameter  
    {
        ///<summary>
        ///输血前检查项目中HBSAG检验结果代码
        ///</summary>
        public static string BLOOD_HBSAG => SystemParm.GetParameterValue("BLOOD_HBSAG", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中HCV检验结果代码
        ///</summary>
        public static string BLOOD_HCV => SystemParm.GetParameterValue("BLOOD_HCV", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中HIV检验结果代码
        ///</summary>
        public static string BLOOD_HIV => SystemParm.GetParameterValue("BLOOD_HIV", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中TP检验结果代码
        ///</summary>
        public static string BLOOD_TP => SystemParm.GetParameterValue("BLOOD_TP", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中HB检验结果代码
        ///</summary>
        public static string BLOOD_HB => SystemParm.GetParameterValue("BLOOD_HB", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中PIT检验结果代码
        ///</summary>
        public static string BLOOD_PIT => SystemParm.GetParameterValue("BLOOD_PIT", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中ABO检验结果代码
        ///</summary>
        public static string BLOOD_ABO => SystemParm.GetParameterValue("BLOOD_ABO", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中RHD检验结果代码
        ///</summary>
        public static string BLOOD_RHD => SystemParm.GetParameterValue("BLOOD_RHD", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
        ///<summary>
        ///输血前检查项目中血型抗体筛检实验检验结果代码
        ///</summary>
        public static string BLOOD_SCREENING_TEST => SystemParm.GetParameterValue("BLOOD_SCREENING_TEST", "OBDOCTWS", SystemParm.LoginUser.DEPT_CODE, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);

        private static string applab_Deptcode;
        /// <summary>
        /// 费别
        /// </summary>
        public static string Applab_Deptcode
        {
            get
            {
                applab_Deptcode = SystemParm.GetParameterValue("APPLAB_DEPTCODE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return applab_Deptcode;
            }
            private set
            {
               applab_Deptcode = value;
            }
        }

        private static string chargeType;
        /// <summary>
        /// 费别
        /// </summary>
        public static string ChargeType
        {
            get
            {
                chargeType = SystemParm.GetParameterValue("CHARGETYPE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return chargeType;
            }
            private set
            {
               chargeType = value;
            }
        }

        private static string examBranchFiler;
        /// <summary>
        /// 医生站检查类别是否分支机构过滤 1过滤 0不过滤
        /// </summary>
        public static string ExamBranchFiler
        {
            get
            {
                examBranchFiler = SystemParm.GetParameterValue("EXAM_BRANCH_FILER", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return examBranchFiler;
            }
            private set
            {
               examBranchFiler = value;
            }
        }

        private static string cdssFlag;
        /// <summary>
        /// 灵医智慧调用
        /// </summary>
        public static string CdssFlag
        {
            get
            {
                cdssFlag = SystemParm.GetParameterValue("CDSS_FLAG", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return cdssFlag;
            }
            private set
            {
               cdssFlag = value;
            }
        }

        private static string notExamApply;
        /// <summary>
        /// 灵医智慧调用
        /// </summary>
        public static string NotExamApply
        {
            get
            {
                notExamApply = SystemParm.GetParameterValue("NOT_EXAM_APPLY", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return notExamApply;
            }
            private set
            {
               notExamApply = value;
            }
        }

        private static string examPathologyWomanRequiredDept;
        /// <summary>
        /// 获取检查病理特殊说明 妇产科科室 校验是否填写月经是否为必填项等 YYS 2021.5.12
        /// </summary>
        public static string ExamPathologyWomanRequiredDept
        {
            get
            {
                examPathologyWomanRequiredDept = SystemParm.GetParameterValue("EXAM_PATHOLOGY_WOMAN_REQUIRED_DEPT", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return examPathologyWomanRequiredDept;
            }
            private set
            {
                examPathologyWomanRequiredDept = value;
            }
        }

        public static void Clear()
        {
            NotExamApply = "";
            CdssFlag = "";
            ExamBranchFiler = "";
            Applab_Deptcode = "";
            ChargeType = "";
        }
        

        
    }
    /// <summary>
    /// 就诊类参数
    /// </summary>
    public class ClinicParameter
    {
        public static void Clear()
        {
            ClinicDays = 0;
            ClinicedDays = 0;
            ClinicHistoryDays = 0;
            TransferClinic = string.Empty;
            TransferCheck = string.Empty;
        }
        private static int clinicDays;
        /// <summary>
        /// 就疗天数
        /// </summary>
        public static int ClinicDays
        {
            get
            {
                try
                {
                    clinicDays = int.Parse(SystemParm.GetParameterValue("CLINIC_DAYS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode));
                }
                catch
                {
                    clinicDays = 3;
                }
                return clinicDays;
            }
            private set
            {
                clinicDays = value;
            }
        }
        private static int clinicedDays;
        /// <summary>
        /// 已诊天数
        /// </summary>
        public static int ClinicedDays
        {
            get
            {
                try
                {
                    clinicedDays = int.Parse(SystemParm.GetParameterValue("CLINICED_DAYS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode));
                }
                catch
                {
                    clinicedDays = 14;
                }
                return clinicedDays;
            }
            private set
            {
                clinicedDays = value;
            }
        }
        private static int clinicHistoryDays;
        /// <summary>
        /// 历史就诊天数
        /// </summary>
        public static int ClinicHistoryDays
        {
            get
            {
                try
                {
                    clinicHistoryDays = int.Parse(SystemParm.GetParameterValue("CLINIC_HISTORY_DAYS", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode));
                }
                catch
                {
                    clinicHistoryDays = 300;
                }
                return clinicHistoryDays;
            }
            private set
            {
                clinicHistoryDays = value;
            }
        }
        private static string transferClinic;
        /// <summary>
        /// 转诊方式
        /// </summary>
        public static string TransferClinic
        {
            get
            {
                transferClinic = SystemParm.GetParameterValue("TRANSFER_CLINIC", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return transferClinic;
            }
            private set
            {
                transferClinic = value;
            }
        }
        private static string transferCheck;
        /// <summary>
        /// 转诊检查
        /// </summary>
        public static string TransferCheck
        {
            get
            {
                transferCheck = SystemParm.GetParameterValue("TRANSFER_CHECK", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return transferCheck;
            }
            private set
            {
                transferCheck = value;
            }
        }

        private static string saveAutoPrint;
        /// <summary>
        /// 处方、检查、检验、处置保存自动打印
        /// </summary>
        public static string SaveAutoPrint
        {
            get
            {
                saveAutoPrint = SystemParm.GetParameterValue("SAVE_AUTO_PRINT", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return saveAutoPrint;
            }
            private set
            {
                saveAutoPrint = value;
            }
        }

        private static string _MR_REQUIRED;
        /// <summary>
        /// 维护规则，例如门诊病历表（OUTP_MR）中ILLNESS_DESC字段为必填项配置为ILLNESS_DESC，不是必填项不写，多个必填项以[;]分隔
        /// </summary>
        public static string MR_REQUIRED
        {
            get
            {
                _MR_REQUIRED = SystemParm.GetParameterValue("MR_REQUIRED", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _MR_REQUIRED;
            }
            private set
            {
                _MR_REQUIRED = value;
            }
        }
    }
    /// <summary>
    /// 其他参数
    /// </summary>
    public class OtherParameter
    {
        public static void Clear()
        {
            ViewVersion = string.Empty;
            IS_FUZZY_FILTER = string.Empty;
            PANORAMA_URL = string.Empty;
            IsSave662Data = null;
            DCS_SERVICE_URL = string.Empty;
            CALL_HF = "";
            REFORM_CHARGE_TYPE = "";
            YB_CHARGE_TYPE = "";
            OUTP_EMR = "";
            FIRM_CALLMODE = "";
            KNOWLEDGEBASE_ENABLED = "";
            OFFLINE_INSURANCE = "";
            PACS_IMAGE_PATH = "";
        }
        private static string viewVersion;
        /// <summary>
        /// 主页面显示风格，1综合医嘱与电子病历平铺，2综合医嘱和电子病历tab页显示，3电子病历、处方、草药处方、检查、检验、处置、已开检查、已开检验tab页切换
        /// </summary>
        public static string ViewVersion
        {
            get
            {
                if (string.IsNullOrEmpty(viewVersion))
                {
                    string ver = SystemParm.GetParameterValue("VIEW_VERSION", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                    viewVersion = ver;
                }
                return viewVersion;
            }
           set
            {
                viewVersion = value;
            }
        }
        private static string isFuzzyFilter;
        /// <summary>
        /// 输入法模糊匹配
        /// </summary>
        public static string IS_FUZZY_FILTER
        {
            get
            {
                isFuzzyFilter = SystemParm.GetParameterValue("IS_FUZZY_FILTER", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return isFuzzyFilter;
            }
            private set
            {
                isFuzzyFilter = value;
            }
        }

        private static bool? _isSave662Data;
        public static bool? IsSave662Data
        {
            get
            {
                _isSave662Data = Convert.ToBoolean(SystemParm.GetParameterValue("IS_SAVE_662_DATE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode));
                return (bool)_isSave662Data;
            }
            private set
            {
                _isSave662Data = value;
            }
        }
        private static string _PANORAMA_URL;

        public static string PANORAMA_URL
        {
            get
            {
                PublicFunction.GetInterfaceConfigDict("PANORAMA_URL", ref _PANORAMA_URL);
                return _PANORAMA_URL;
            }
            private set
            {
                _PANORAMA_URL = value;
            }
        }

        private static string _DCS_SERVICE_URL;
        public static string DCS_SERVICE_URL
        {
            get
            {
                PublicFunction.GetInterfaceConfigDict("DCS_SERVICE_URL", ref _DCS_SERVICE_URL);
                return _DCS_SERVICE_URL;
            }
            private set
            {
                _DCS_SERVICE_URL = value;
            }
        }

        private static string _CALL_HF;
        public static string CALL_HF
        {
            get
            {
                _CALL_HF = SystemParm.GetParameterValue("CALL_HF", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _CALL_HF;
            }
            private set
            {
                _CALL_HF = value;
            }
        }

        private static string _KNOWLEDGEBASE_ENABLED;
        public static string KNOWLEDGEBASE_ENABLED
        {
            get
            {
                _KNOWLEDGEBASE_ENABLED = SystemParm.GetParameterValue("KNOWLEDGEBASE_ENABLED", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _KNOWLEDGEBASE_ENABLED;
            }
            private set
            {
                _KNOWLEDGEBASE_ENABLED = value;
            }
        }

        private static string _FIRM_CALLMODE;
        public static string FIRM_CALLMODE
        {
            get
            {
                _FIRM_CALLMODE = SystemParm.GetParameterValue("FIRM_CALLMODE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _FIRM_CALLMODE;
            }
            private set
            {
                _FIRM_CALLMODE = value;
            }
        }

        private static string _OUTP_EMR;
        /// <summary>
        /// 是否启用电子病历，1启动，0不启用
        /// </summary>
        public static string OUTP_EMR
        {
            get
            {
                string temp = SystemParm.GetParameterValue("OUTP_EMR", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                _OUTP_EMR = temp;
                return _OUTP_EMR;
            }
            private set
            {
                _OUTP_EMR = value;
            }
        }


        private static string _YB_CHARGE_TYPE;
        /// <summary>
        /// 哪些费别是医保费别
        /// </summary>
        public static string YB_CHARGE_TYPE
        {
            get
            {
                _YB_CHARGE_TYPE = SystemParm.GetParameterValue("YB_CHARGE_TYPE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _YB_CHARGE_TYPE;
            }
            private set
            {
                _YB_CHARGE_TYPE = value;
            }

        }
        private static string _REFORM_CHARGE_TYPE;

        public static string REFORM_CHARGE_TYPE
        {
            get
            {
                _REFORM_CHARGE_TYPE = SystemParm.GetParameterValue("REFORM_CHARGE_TYPE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _REFORM_CHARGE_TYPE;
            }
            private set
            {
                _REFORM_CHARGE_TYPE = value;
            }
        }

        private static string _OFFLINE_INSURANCE;
        public static string OFFLINE_INSURANCE
        {
            get
            {
                _OFFLINE_INSURANCE = SystemParm.GetParameterValue("OFFLINE_INSURANCE", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _OFFLINE_INSURANCE;
            }
            private set
            {
                _OFFLINE_INSURANCE = value;
            }
        }
        private static string _PACS_IMAGE_PATH;
        public static string PACS_IMAGE_PATH
        {
            get
            {
                _PACS_IMAGE_PATH = SystemParm.GetParameterValue("PACS_IMAGE_PATH", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _PACS_IMAGE_PATH;
            }
            private set
            {
                _PACS_IMAGE_PATH = value;
            }
        }

        /// <summary>
        /// 检验报告结果危急值计时器，如果有当日的检验报告在表LAB.LAB_RESULT_WJZ中，则弹出检验危机值提醒窗口。
        /// </summary>
        private static string _LABCRITICALVALUETIMER;
        public static string LABCRITICALVALUETIMER
        {
            get
            {
                _LABCRITICALVALUETIMER = SystemParm.GetParameterValue("LABCRITICALVALUETIMER", "OUTPDOCT", GlobalValue.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                return _LABCRITICALVALUETIMER;
            }
            private set
            {
                _LABCRITICALVALUETIMER = value;
            }
        }
    }
}
